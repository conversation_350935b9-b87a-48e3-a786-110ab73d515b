"""
事件管理器 - 提供发布订阅模式的事件系统
"""

import threading
import time
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict
import weakref


@dataclass
class Event:
    """事件数据类"""
    event_type: str
    data: Any = None
    timestamp: float = field(default_factory=time.time)
    source: str = ""
    metadata: Dict = field(default_factory=dict)


class EventManager:
    """事件管理器"""
    
    def __init__(self, max_history: int = 1000):
        self._subscribers = defaultdict(list)  # event_type -> [callbacks]
        self._global_subscribers = []  # 全局订阅者
        self._event_history = []  # 事件历史
        self._lock = threading.RLock()
        self._max_history = max_history
        
        # 统计信息
        self._event_count = 0
        self._subscription_count = 0
    
    def subscribe(self, event_type: str, callback: Callable[[Event], None], 
                 weak_ref: bool = False) -> bool:
        """订阅事件"""
        try:
            with self._lock:
                if weak_ref:
                    # 使用弱引用，避免循环引用
                    callback_ref = weakref.ref(callback)
                    self._subscribers[event_type].append(callback_ref)
                else:
                    self._subscribers[event_type].append(callback)
                
                self._subscription_count += 1
                return True
        except Exception as e:
            print(f"订阅事件失败: {e}")
            return False
    
    def subscribe_global(self, callback: Callable[[Event], None], 
                        weak_ref: bool = False) -> bool:
        """订阅所有事件"""
        try:
            with self._lock:
                if weak_ref:
                    callback_ref = weakref.ref(callback)
                    self._global_subscribers.append(callback_ref)
                else:
                    self._global_subscribers.append(callback)
                
                self._subscription_count += 1
                return True
        except Exception as e:
            print(f"订阅全局事件失败: {e}")
            return False
    
    def unsubscribe(self, event_type: str, callback: Callable[[Event], None]) -> bool:
        """取消订阅事件"""
        try:
            with self._lock:
                subscribers = self._subscribers[event_type]
                
                # 查找并移除回调
                for i, subscriber in enumerate(subscribers):
                    # 处理弱引用
                    if isinstance(subscriber, weakref.ref):
                        if subscriber() == callback:
                            subscribers.pop(i)
                            self._subscription_count -= 1
                            return True
                    else:
                        if subscriber == callback:
                            subscribers.pop(i)
                            self._subscription_count -= 1
                            return True
                
                return False
        except Exception as e:
            print(f"取消订阅事件失败: {e}")
            return False
    
    def unsubscribe_global(self, callback: Callable[[Event], None]) -> bool:
        """取消订阅全局事件"""
        try:
            with self._lock:
                for i, subscriber in enumerate(self._global_subscribers):
                    # 处理弱引用
                    if isinstance(subscriber, weakref.ref):
                        if subscriber() == callback:
                            self._global_subscribers.pop(i)
                            self._subscription_count -= 1
                            return True
                    else:
                        if subscriber == callback:
                            self._global_subscribers.pop(i)
                            self._subscription_count -= 1
                            return True
                
                return False
        except Exception as e:
            print(f"取消订阅全局事件失败: {e}")
            return False
    
    def publish(self, event_type: str, data: Any = None, source: str = "", 
               metadata: Dict = None) -> bool:
        """发布事件"""
        try:
            with self._lock:
                # 创建事件对象
                event = Event(
                    event_type=event_type,
                    data=data,
                    source=source,
                    metadata=metadata or {}
                )
                
                # 添加到历史记录
                self._add_to_history(event)
                
                # 通知特定事件订阅者
                self._notify_subscribers(event_type, event)
                
                # 通知全局订阅者
                self._notify_global_subscribers(event)
                
                self._event_count += 1
                return True
                
        except Exception as e:
            print(f"发布事件失败: {e}")
            return False
    
    def _notify_subscribers(self, event_type: str, event: Event):
        """通知特定事件的订阅者"""
        subscribers = self._subscribers[event_type].copy()  # 复制列表避免并发修改
        dead_refs = []
        
        for subscriber in subscribers:
            try:
                # 处理弱引用
                if isinstance(subscriber, weakref.ref):
                    callback = subscriber()
                    if callback is None:
                        # 弱引用已失效
                        dead_refs.append(subscriber)
                        continue
                else:
                    callback = subscriber
                
                # 调用回调函数
                callback(event)
                
            except Exception as e:
                print(f"事件回调执行失败 ({event_type}): {e}")
        
        # 清理失效的弱引用
        for dead_ref in dead_refs:
            try:
                self._subscribers[event_type].remove(dead_ref)
                self._subscription_count -= 1
            except ValueError:
                pass
    
    def _notify_global_subscribers(self, event: Event):
        """通知全局订阅者"""
        subscribers = self._global_subscribers.copy()  # 复制列表避免并发修改
        dead_refs = []
        
        for subscriber in subscribers:
            try:
                # 处理弱引用
                if isinstance(subscriber, weakref.ref):
                    callback = subscriber()
                    if callback is None:
                        # 弱引用已失效
                        dead_refs.append(subscriber)
                        continue
                else:
                    callback = subscriber
                
                # 调用回调函数
                callback(event)
                
            except Exception as e:
                print(f"全局事件回调执行失败: {e}")
        
        # 清理失效的弱引用
        for dead_ref in dead_refs:
            try:
                self._global_subscribers.remove(dead_ref)
                self._subscription_count -= 1
            except ValueError:
                pass
    
    def _add_to_history(self, event: Event):
        """添加事件到历史记录"""
        self._event_history.append(event)
        
        # 限制历史记录数量
        if len(self._event_history) > self._max_history:
            self._event_history = self._event_history[-self._max_history:]
    
    def get_event_history(self, event_type: str = None, limit: int = None) -> List[Event]:
        """获取事件历史"""
        with self._lock:
            history = self._event_history.copy()
            
            # 按事件类型过滤
            if event_type:
                history = [e for e in history if e.event_type == event_type]
            
            # 限制数量
            if limit:
                history = history[-limit:]
            
            return history
    
    def clear_history(self):
        """清空事件历史"""
        with self._lock:
            self._event_history.clear()
    
    def get_subscribers_count(self, event_type: str = None) -> int:
        """获取订阅者数量"""
        with self._lock:
            if event_type:
                return len(self._subscribers[event_type])
            else:
                return self._subscription_count
    
    def get_event_types(self) -> List[str]:
        """获取所有事件类型"""
        with self._lock:
            return list(self._subscribers.keys())
    
    def has_subscribers(self, event_type: str) -> bool:
        """检查是否有订阅者"""
        with self._lock:
            return len(self._subscribers[event_type]) > 0 or len(self._global_subscribers) > 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            event_type_stats = {}
            for event_type, subscribers in self._subscribers.items():
                event_type_stats[event_type] = {
                    'subscriber_count': len(subscribers),
                    'recent_events': len([e for e in self._event_history 
                                        if e.event_type == event_type and 
                                        time.time() - e.timestamp < 3600])  # 最近1小时
                }
            
            return {
                'total_events': self._event_count,
                'total_subscriptions': self._subscription_count,
                'global_subscribers': len(self._global_subscribers),
                'event_types': len(self._subscribers),
                'history_size': len(self._event_history),
                'event_type_stats': event_type_stats
            }
    
    def cleanup_dead_references(self) -> int:
        """清理失效的弱引用"""
        with self._lock:
            cleaned_count = 0
            
            # 清理特定事件订阅者中的失效引用
            for event_type, subscribers in self._subscribers.items():
                dead_refs = []
                for subscriber in subscribers:
                    if isinstance(subscriber, weakref.ref) and subscriber() is None:
                        dead_refs.append(subscriber)
                
                for dead_ref in dead_refs:
                    subscribers.remove(dead_ref)
                    cleaned_count += 1
            
            # 清理全局订阅者中的失效引用
            dead_refs = []
            for subscriber in self._global_subscribers:
                if isinstance(subscriber, weakref.ref) and subscriber() is None:
                    dead_refs.append(subscriber)
            
            for dead_ref in dead_refs:
                self._global_subscribers.remove(dead_ref)
                cleaned_count += 1
            
            self._subscription_count -= cleaned_count
            return cleaned_count
    
    def reset(self):
        """重置事件管理器"""
        with self._lock:
            self._subscribers.clear()
            self._global_subscribers.clear()
            self._event_history.clear()
            self._event_count = 0
            self._subscription_count = 0
    
    # 便捷方法
    def once(self, event_type: str, callback: Callable[[Event], None]) -> bool:
        """订阅事件一次（事件触发后自动取消订阅）"""
        def once_wrapper(event: Event):
            try:
                callback(event)
            finally:
                self.unsubscribe(event_type, once_wrapper)
        
        return self.subscribe(event_type, once_wrapper)
    
    def emit(self, event_type: str, **kwargs) -> bool:
        """发布事件的便捷方法"""
        return self.publish(event_type, data=kwargs)
    
    def wait_for_event(self, event_type: str, timeout: float = None) -> Optional[Event]:
        """等待特定事件发生"""
        import threading
        
        event_received = threading.Event()
        received_event = None
        
        def event_handler(event: Event):
            nonlocal received_event
            received_event = event
            event_received.set()
        
        # 订阅事件
        self.subscribe(event_type, event_handler)
        
        try:
            # 等待事件
            if event_received.wait(timeout):
                return received_event
            else:
                return None
        finally:
            # 清理订阅
            self.unsubscribe(event_type, event_handler)
    
    def create_event_filter(self, condition: Callable[[Event], bool]) -> Callable[[Callable[[Event], None]], Callable[[Event], None]]:
        """创建事件过滤器装饰器"""
        def decorator(callback: Callable[[Event], None]) -> Callable[[Event], None]:
            def filtered_callback(event: Event):
                if condition(event):
                    callback(event)
            return filtered_callback
        return decorator
    
    def batch_publish(self, events: List[Dict[str, Any]]) -> int:
        """批量发布事件"""
        success_count = 0
        for event_data in events:
            if self.publish(**event_data):
                success_count += 1
        return success_count
    
    def __del__(self):
        """析构函数"""
        try:
            self.reset()
        except Exception:
            pass