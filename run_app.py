#!/usr/bin/env python3
"""
HubStudio 视频上传工具启动脚本
重构版本 2.0
"""

import sys
import os
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print(f"错误: 需要Python 3.9或更高版本，当前版本: {sys.version}")
        return False
    return True

def check_tkinter():
    """检查Tkinter是否可用"""
    try:
        import tkinter
        # 尝试创建一个测试窗口
        root = tkinter.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        return True
    except Exception as e:
        print(f"错误: Tkinter不可用: {e}")
        print("请确保已安装Python的Tkinter模块")
        return False

def check_dependencies():
    """检查依赖是否安装"""
    # 包名映射：导入名 -> 包名
    required_packages = {
        'customtkinter': 'customtkinter',
        'requests': 'requests',
        'cryptography': 'cryptography',
        'pydantic': 'pydantic',
        'PIL': 'pillow'  # PIL是pillow的导入名
    }

    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} 未安装")

    if missing_packages:
        print("\n错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        print("\n或者单独安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✓ 所有依赖包已安装")
    return True

def main():
    """主函数"""
    print("HubStudio 视频上传工具 v2.0")
    print("=" * 40)

    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        sys.exit(1)

    # 检查Tkinter
    if not check_tkinter():
        input("按回车键退出...")
        sys.exit(1)

    # 检查依赖
    print("\n正在检查依赖包...")
    if not check_dependencies():
        input("按回车键退出...")
        sys.exit(1)

    try:
        print("\n正在启动应用...")
        # 导入并运行主应用
        from src.main import main as app_main
        app_main()

    except KeyboardInterrupt:
        print("\n应用已被用户中断")
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请检查项目文件是否完整")
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"启动应用失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
