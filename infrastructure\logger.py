"""
统一日志管理器 - 提供结构化日志服务
"""

import threading
import time
from queue import Queue, Empty
from typing import List, Dict, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import sys


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LogMessage:
    """日志消息数据类"""
    timestamp: float
    level: LogLevel
    component: str
    message: str
    extra: Dict = None
    
    def __post_init__(self):
        if self.extra is None:
            self.extra = {}


class LogManager:
    """统一日志管理器"""
    
    def __init__(self, max_memory_logs: int = 1000):
        self.max_memory_logs = max_memory_logs
        self.memory_logs = []
        self.handlers = []
        self.log_queue = Queue()
        self.observers = []
        self._lock = threading.RLock()
        self._running = False
        self._worker_thread = None
        
        # 配置loguru
        self._setup_loguru()
        
        # 启动日志处理线程
        self.start()
    
    def _setup_loguru(self):
        """配置loguru日志系统"""
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            level="INFO",
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            colorize=True
        )
        
        # 添加文件处理器
        logger.add(
            "logs/app_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="30 days",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            encoding="utf-8"
        )
    
    def start(self):
        """启动日志处理线程"""
        if not self._running:
            self._running = True
            self._worker_thread = threading.Thread(target=self._process_logs, daemon=True)
            self._worker_thread.start()
    
    def stop(self):
        """停止日志处理线程"""
        self._running = False
        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=1.0)
    
    def _process_logs(self):
        """处理日志队列"""
        while self._running:
            try:
                # 从队列获取日志消息
                log_msg = self.log_queue.get(timeout=0.1)
                
                # 添加到内存日志
                with self._lock:
                    self.memory_logs.append(log_msg)
                    # 保持内存日志数量限制
                    if len(self.memory_logs) > self.max_memory_logs:
                        self.memory_logs.pop(0)
                
                # 通知观察者
                self._notify_observers(log_msg)
                
                # 使用loguru记录日志
                self._log_with_loguru(log_msg)
                
            except Empty:
                continue
            except Exception as e:
                # 避免日志系统本身的错误导致程序崩溃
                print(f"日志处理错误: {e}")
    
    def _log_with_loguru(self, log_msg: LogMessage):
        """使用loguru记录日志"""
        message = f"[{log_msg.component}] {log_msg.message}"
        
        if log_msg.level == LogLevel.DEBUG:
            logger.debug(message)
        elif log_msg.level == LogLevel.INFO:
            logger.info(message)
        elif log_msg.level == LogLevel.WARNING:
            logger.warning(message)
        elif log_msg.level == LogLevel.ERROR:
            logger.error(message)
        elif log_msg.level == LogLevel.CRITICAL:
            logger.critical(message)
    
    def log(self, level: LogLevel, message: str, component: str = "System", **kwargs):
        """记录日志"""
        log_msg = LogMessage(
            timestamp=time.time(),
            level=level,
            component=component,
            message=message,
            extra=kwargs
        )
        
        # 添加到队列
        self.log_queue.put(log_msg)
    
    def debug(self, message: str, component: str = "System", **kwargs):
        """记录调试日志"""
        self.log(LogLevel.DEBUG, message, component, **kwargs)
    
    def info(self, message: str, component: str = "System", **kwargs):
        """记录信息日志"""
        self.log(LogLevel.INFO, message, component, **kwargs)
    
    def warning(self, message: str, component: str = "System", **kwargs):
        """记录警告日志"""
        self.log(LogLevel.WARNING, message, component, **kwargs)
    
    def error(self, message: str, component: str = "System", **kwargs):
        """记录错误日志"""
        self.log(LogLevel.ERROR, message, component, **kwargs)
    
    def critical(self, message: str, component: str = "System", **kwargs):
        """记录严重错误日志"""
        self.log(LogLevel.CRITICAL, message, component, **kwargs)
    
    def get_logs(self, component: Optional[str] = None, 
                 level: Optional[LogLevel] = None, 
                 limit: Optional[int] = None) -> List[LogMessage]:
        """获取日志"""
        with self._lock:
            logs = self.memory_logs.copy()
        
        # 过滤组件
        if component:
            logs = [log for log in logs if log.component == component]
        
        # 过滤级别
        if level:
            logs = [log for log in logs if log.level == level]
        
        # 限制数量
        if limit:
            logs = logs[-limit:]
        
        return logs
    
    def get_recent_logs(self, minutes: int = 10) -> List[LogMessage]:
        """获取最近的日志"""
        cutoff_time = time.time() - (minutes * 60)
        with self._lock:
            return [log for log in self.memory_logs if log.timestamp >= cutoff_time]
    
    def clear_logs(self):
        """清除内存日志"""
        with self._lock:
            self.memory_logs.clear()
    
    def add_observer(self, observer: Callable[[LogMessage], None]):
        """添加日志观察者"""
        if observer not in self.observers:
            self.observers.append(observer)
    
    def remove_observer(self, observer: Callable[[LogMessage], None]):
        """移除日志观察者"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def _notify_observers(self, log_msg: LogMessage):
        """通知观察者"""
        for observer in self.observers:
            try:
                observer(log_msg)
            except Exception as e:
                # 避免观察者错误影响日志系统
                print(f"日志观察者错误: {e}")
    
    def get_statistics(self) -> Dict:
        """获取日志统计信息"""
        with self._lock:
            total_logs = len(self.memory_logs)
            level_counts = {}
            component_counts = {}
            
            for log in self.memory_logs:
                # 统计级别
                level_name = log.level.value
                level_counts[level_name] = level_counts.get(level_name, 0) + 1
                
                # 统计组件
                component_counts[log.component] = component_counts.get(log.component, 0) + 1
        
        return {
            'total_logs': total_logs,
            'level_counts': level_counts,
            'component_counts': component_counts,
            'queue_size': self.log_queue.qsize()
        }
    
    def __del__(self):
        """析构函数"""
        self.stop()


# 全局日志管理器实例
_global_log_manager = None


def get_logger() -> LogManager:
    """获取全局日志管理器实例"""
    global _global_log_manager
    if _global_log_manager is None:
        _global_log_manager = LogManager()
    return _global_log_manager