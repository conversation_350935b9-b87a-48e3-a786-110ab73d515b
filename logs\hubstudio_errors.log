2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028636DE4510>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:45:17 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\.venv\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D11F020640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:52:05 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 刷新环境列表异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/list (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D11EF8A8B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016CBA3A0640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002007F0D4640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\.venv\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
