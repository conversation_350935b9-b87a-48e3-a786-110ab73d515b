"""
状态管理器 - 提供线程安全的全局状态管理
"""

import threading
import time
from typing import Any, Dict, List, Callable, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
import json


class StateChangeType(Enum):
    """状态变更类型"""
    SET = "set"
    UPDATE = "update"
    DELETE = "delete"
    BATCH_UPDATE = "batch_update"


@dataclass
class StateChange:
    """状态变更事件"""
    change_type: StateChangeType
    key: str
    old_value: Any = None
    new_value: Any = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict = field(default_factory=dict)


class StateManager:
    """线程安全的状态管理器"""
    
    def __init__(self):
        self._state = {}
        self._observers = defaultdict(list)  # key -> [observers]
        self._global_observers = []  # 全局观察者
        self._lock = threading.RLock()
        self._history = []  # 状态变更历史
        self._max_history = 1000  # 最大历史记录数
        
        # 初始化默认状态
        self._initialize_default_state()
    
    def _initialize_default_state(self):
        """初始化默认状态"""
        default_state = {
            # 应用程序状态
            'app_initialized': False,
            'app_running': False,
            
            # 上传状态
            'upload_active': False,
            'upload_paused': False,
            'upload_progress': 0,
            'total_videos': 0,
            'completed_videos': 0,
            'failed_videos': 0,
            
            # 并发状态
            'active_tasks': 0,
            'task_queue_size': 0,
            'max_concurrent': 3,
            'max_browsers': 5,
            
            # 环境状态
            'environments_loaded': False,
            'available_environments': [],
            'active_environments': [],
            
            # API状态
            'api_connected': False,
            'api_last_check': 0,
            'api_error_count': 0,
            
            # 文件状态
            'selected_files': [],
            'file_count': 0,
            
            # 配置状态
            'config_loaded': False,
            'config_valid': False,
            
            # 错误状态
            'last_error': None,
            'error_count': 0,
            
            # 统计信息
            'session_start_time': time.time(),
            'total_upload_time': 0,
            'average_upload_time': 0
        }
        
        with self._lock:
            self._state.update(default_state)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取状态值"""
        with self._lock:
            return self._state.get(key, default)
    
    def set(self, key: str, value: Any, metadata: Dict = None) -> bool:
        """设置状态值"""
        try:
            with self._lock:
                old_value = self._state.get(key)
                self._state[key] = value
                
                # 记录变更
                change = StateChange(
                    change_type=StateChangeType.SET,
                    key=key,
                    old_value=old_value,
                    new_value=value,
                    metadata=metadata or {}
                )
                self._add_to_history(change)
                
                # 通知观察者
                self._notify_observers(key, change)
                
                return True
        except Exception as e:
            print(f"设置状态失败: {e}")
            return False
    
    def update(self, key: str, updater: Callable[[Any], Any], metadata: Dict = None) -> bool:
        """使用更新函数更新状态值"""
        try:
            with self._lock:
                old_value = self._state.get(key)
                new_value = updater(old_value)
                self._state[key] = new_value
                
                # 记录变更
                change = StateChange(
                    change_type=StateChangeType.UPDATE,
                    key=key,
                    old_value=old_value,
                    new_value=new_value,
                    metadata=metadata or {}
                )
                self._add_to_history(change)
                
                # 通知观察者
                self._notify_observers(key, change)
                
                return True
        except Exception as e:
            print(f"更新状态失败: {e}")
            return False
    
    def batch_update(self, updates: Dict[str, Any], metadata: Dict = None) -> bool:
        """批量更新状态"""
        try:
            with self._lock:
                changes = []
                
                for key, value in updates.items():
                    old_value = self._state.get(key)
                    self._state[key] = value
                    
                    change = StateChange(
                        change_type=StateChangeType.BATCH_UPDATE,
                        key=key,
                        old_value=old_value,
                        new_value=value,
                        metadata=metadata or {}
                    )
                    changes.append(change)
                    self._add_to_history(change)
                
                # 通知观察者
                for change in changes:
                    self._notify_observers(change.key, change)
                
                return True
        except Exception as e:
            print(f"批量更新状态失败: {e}")
            return False
    
    def delete(self, key: str, metadata: Dict = None) -> bool:
        """删除状态值"""
        try:
            with self._lock:
                if key in self._state:
                    old_value = self._state[key]
                    del self._state[key]
                    
                    # 记录变更
                    change = StateChange(
                        change_type=StateChangeType.DELETE,
                        key=key,
                        old_value=old_value,
                        metadata=metadata or {}
                    )
                    self._add_to_history(change)
                    
                    # 通知观察者
                    self._notify_observers(key, change)
                    
                    return True
                return False
        except Exception as e:
            print(f"删除状态失败: {e}")
            return False
    
    def has(self, key: str) -> bool:
        """检查状态是否存在"""
        with self._lock:
            return key in self._state
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有状态"""
        with self._lock:
            return self._state.copy()
    
    def get_keys(self) -> List[str]:
        """获取所有状态键"""
        with self._lock:
            return list(self._state.keys())
    
    def clear(self, preserve_defaults: bool = True) -> bool:
        """清空状态"""
        try:
            with self._lock:
                if preserve_defaults:
                    # 保留默认状态，重置为初始值
                    self._state.clear()
                    self._initialize_default_state()
                else:
                    # 完全清空
                    self._state.clear()
                
                # 清空历史记录
                self._history.clear()
                
                return True
        except Exception as e:
            print(f"清空状态失败: {e}")
            return False
    
    def subscribe(self, key: str, observer: Callable[[StateChange], None]):
        """订阅特定键的状态变更"""
        with self._lock:
            if observer not in self._observers[key]:
                self._observers[key].append(observer)
    
    def subscribe_global(self, observer: Callable[[StateChange], None]):
        """订阅所有状态变更"""
        with self._lock:
            if observer not in self._global_observers:
                self._global_observers.append(observer)
    
    def unsubscribe(self, key: str, observer: Callable[[StateChange], None]):
        """取消订阅特定键的状态变更"""
        with self._lock:
            if observer in self._observers[key]:
                self._observers[key].remove(observer)
    
    def unsubscribe_global(self, observer: Callable[[StateChange], None]):
        """取消订阅所有状态变更"""
        with self._lock:
            if observer in self._global_observers:
                self._global_observers.remove(observer)
    
    def _notify_observers(self, key: str, change: StateChange):
        """通知观察者"""
        # 通知特定键的观察者
        for observer in self._observers[key]:
            try:
                observer(change)
            except Exception as e:
                print(f"状态观察者错误 ({key}): {e}")
        
        # 通知全局观察者
        for observer in self._global_observers:
            try:
                observer(change)
            except Exception as e:
                print(f"全局状态观察者错误: {e}")
    
    def _add_to_history(self, change: StateChange):
        """添加到历史记录"""
        self._history.append(change)
        
        # 限制历史记录数量
        if len(self._history) > self._max_history:
            self._history = self._history[-self._max_history:]
    
    def get_history(self, key: str = None, limit: int = None) -> List[StateChange]:
        """获取状态变更历史"""
        with self._lock:
            history = self._history
            
            # 按键过滤
            if key:
                history = [change for change in history if change.key == key]
            
            # 限制数量
            if limit:
                history = history[-limit:]
            
            return history.copy()
    
    def get_statistics(self) -> Dict:
        """获取状态统计信息"""
        with self._lock:
            return {
                'total_keys': len(self._state),
                'history_count': len(self._history),
                'observer_count': sum(len(observers) for observers in self._observers.values()),
                'global_observer_count': len(self._global_observers),
                'memory_usage': self._estimate_memory_usage()
            }
    
    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        try:
            import sys
            total_size = 0
            
            # 状态数据大小
            total_size += sys.getsizeof(self._state)
            for key, value in self._state.items():
                total_size += sys.getsizeof(key) + sys.getsizeof(value)
            
            # 历史记录大小
            total_size += sys.getsizeof(self._history)
            for change in self._history:
                total_size += sys.getsizeof(change)
            
            return total_size
        except Exception:
            return 0
    
    def export_state(self, include_history: bool = False) -> Dict:
        """导出状态数据"""
        with self._lock:
            export_data = {
                'state': self._state.copy(),
                'timestamp': time.time()
            }
            
            if include_history:
                export_data['history'] = [
                    {
                        'change_type': change.change_type.value,
                        'key': change.key,
                        'old_value': change.old_value,
                        'new_value': change.new_value,
                        'timestamp': change.timestamp,
                        'metadata': change.metadata
                    }
                    for change in self._history
                ]
            
            return export_data
    
    def import_state(self, data: Dict, merge: bool = True) -> bool:
        """导入状态数据"""
        try:
            with self._lock:
                if not merge:
                    self._state.clear()
                
                if 'state' in data:
                    self._state.update(data['state'])
                
                return True
        except Exception as e:
            print(f"导入状态失败: {e}")
            return False
    
    def create_snapshot(self) -> str:
        """创建状态快照"""
        with self._lock:
            snapshot_data = self.export_state(include_history=True)
            return json.dumps(snapshot_data, default=str, ensure_ascii=False)
    
    def restore_snapshot(self, snapshot: str) -> bool:
        """恢复状态快照"""
        try:
            data = json.loads(snapshot)
            return self.import_state(data, merge=False)
        except Exception as e:
            print(f"恢复快照失败: {e}")
            return False
    
    # 便捷方法
    def increment(self, key: str, amount: int = 1) -> bool:
        """递增数值状态"""
        return self.update(key, lambda x: (x or 0) + amount)
    
    def decrement(self, key: str, amount: int = 1) -> bool:
        """递减数值状态"""
        return self.update(key, lambda x: max(0, (x or 0) - amount))
    
    def toggle(self, key: str) -> bool:
        """切换布尔状态"""
        return self.update(key, lambda x: not bool(x))
    
    def append_to_list(self, key: str, item: Any) -> bool:
        """向列表状态添加项目"""
        return self.update(key, lambda x: (x or []) + [item])
    
    def remove_from_list(self, key: str, item: Any) -> bool:
        """从列表状态移除项目"""
        def remove_item(lst):
            if lst and item in lst:
                new_list = lst.copy()
                new_list.remove(item)
                return new_list
            return lst or []
        
        return self.update(key, remove_item)
    
    def add_to_set(self, key: str, item: Any) -> bool:
        """向集合状态添加项目"""
        return self.update(key, lambda x: (x or set()) | {item})
    
    def remove_from_set(self, key: str, item: Any) -> bool:
        """从集合状态移除项目"""
        return self.update(key, lambda x: (x or set()) - {item})