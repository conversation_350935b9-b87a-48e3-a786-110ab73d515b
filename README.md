# HubStudio 视频上传工具

一个基于Python和Tkinter开发的自动化视频上传工具，支持通过HubStudio API批量上传视频到各大视频平台。

## 功能特性

### 🎯 核心功能
- **批量视频上传**: 支持选择多个视频文件或整个文件夹进行批量上传
- **自动化流程**: 通过HubStudio API实现全自动化上传流程
- **多平台支持**: 支持YouTube、B站等主流视频平台
- **智能重试**: 上传失败时自动重试，提高成功率

### 🖥️ 用户界面
- **直观的GUI**: 基于Tkinter的现代化图形界面
- **实时日志**: 实时显示上传进度和状态信息
- **配置管理**: 可视化的配置管理界面
- **环境管理**: 浏览器环境的创建和管理

### ⚡ 高级特性
- **并发上传**: 支持多个视频同时上传，提高效率
- **断点续传**: 支持上传中断后的断点续传
- **模板系统**: 支持视频信息模板，快速配置标题、描述等
- **状态监控**: 实时监控上传状态和系统资源使用情况

## 系统要求

- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python版本**: Python 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **存储空间**: 至少 1GB 可用空间
- **网络**: 稳定的互联网连接

## 快速开始

### 1. 下载项目
```bash
git clone https://github.com/your-username/hubstudio-uploader.git
cd hubstudio-uploader
```

### 2. 启动程序

#### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行
run.bat

# 或在命令行中运行
.\run.bat
```

**Linux/macOS用户：**
```bash
# 添加执行权限
chmod +x run.sh

# 运行脚本
./run.sh
```

#### 方法二：使用启动选择器

```bash
# 安装依赖
pip install -r requirements.txt

# 启动选择器（提供多种启动选项）
python 启动器.py
```

#### 方法三：直接启动特定版本

**简化版GUI（推荐新手）：**
```bash
python start_gui.py
```

**完整版GUI（推荐高级用户）：**
```bash
python main.py
```

### 3. 手动安装（可选）
如果自动脚本无法运行，可以手动安装：

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行程序
python launcher.py
```

## 配置说明

### HubStudio API配置
1. 打开程序后，点击"API配置"标签页
2. 填入您的HubStudio API信息：
   - **API地址**: 通常为 `http://127.0.0.1:50325`
   - **API ID**: 您的HubStudio API ID
   - **API Secret**: 您的HubStudio API密钥
3. 点击"测试连接"验证配置是否正确

### 环境管理
1. 在"环境管理"标签页中管理浏览器环境
2. 选择浏览器类型（Chrome、Firefox、Edge）
3. 点击"刷新列表"获取可用环境
4. 选择环境后点击"启动环境"

### 视频信息配置
1. 在"视频文件"面板中配置视频信息模板
2. 支持使用 `{filename}` 占位符自动替换文件名
3. 可以保存和加载不同的模板配置

## 使用指南

### 基本上传流程

1. **选择视频文件**
   - 点击"选择视频文件"选择单个或多个视频
   - 或点击"选择文件夹"批量添加文件夹中的所有视频

2. **配置视频信息**
   - 设置标题模板（支持 `{filename}` 占位符）
   - 填写描述信息
   - 添加标签（用逗号分隔）
   - 选择隐私设置和分类

3. **开始上传**
   - 确保API连接正常
   - 确保浏览器环境已启动
   - 点击"开始上传"按钮

4. **监控进度**
   - 在日志面板中查看实时上传状态
   - 在控制面板中查看整体进度

### 高级功能

#### 并发上传设置
在配置文件 `config.ini` 中调整并发设置：
```ini
[UPLOAD]
max_concurrent_uploads = 3  # 最大并发上传数
```

#### 自定义模板
1. 配置好视频信息后，点击"保存模板"
2. 下次使用时点击"加载模板"快速应用配置

#### 日志管理
- 点击"清理日志"清空历史日志
- 点击"导出日志"保存日志到文件
- 在系统设置中可以调整日志级别

## 故障排除

### 常见问题

**Q: 程序启动失败，提示缺少依赖**
A: 确保已正确安装所有依赖项，运行 `pip install -r requirements.txt`

**Q: API连接测试失败**
A: 检查以下项目：
- HubStudio服务是否正在运行
- API地址是否正确（通常为 http://127.0.0.1:50325）
- API ID和Secret是否正确
- 防火墙是否阻止了连接

**Q: 环境列表为空**
A: 
- 确保HubStudio服务正常运行
- 点击"刷新列表"重新获取环境
- 检查浏览器类型设置是否正确

**Q: 上传失败**
A: 
- 检查网络连接是否稳定
- 确认视频文件格式是否支持
- 查看日志面板中的详细错误信息
- 尝试重新启动浏览器环境

**Q: 程序运行缓慢**
A: 
- 减少并发上传数量
- 关闭不必要的其他程序
- 确保有足够的系统内存

### 日志文件位置
- Windows: `logs/hubstudio.log`
- Linux/macOS: `logs/hubstudio.log`

### 配置文件位置
- 主配置文件: `config.ini`
- 用户数据: `data/` 目录

## 项目结构

```
hubstudio-uploader/
├── 启动器.py            # 启动选择器（推荐）
├── start_gui.py          # 简化版GUI启动器
├── main.py              # 完整版GUI启动器
├── config.ini           # 配置文件
├── requirements.txt     # 依赖列表
├── run.bat             # Windows启动脚本
├── run.sh              # Linux/macOS启动脚本
├── README.md           # 项目说明
├── infrastructure/     # 基础设施层
│   ├── logger.py       # 日志系统
│   ├── config_manager.py # 配置管理
│   ├── state_manager.py  # 状态管理
│   └── error_handler.py  # 错误处理
├── business/           # 业务逻辑层
│   ├── event_manager.py  # 事件管理
│   ├── concurrency_manager.py # 并发管理
│   ├── environment_manager.py # 环境管理
│   └── upload_manager.py # 上传管理
├── api/                # API接口层
│   ├── hubstudio_api.py  # HubStudio API
│   ├── browser_manager.py # 浏览器管理
│   └── file_manager.py   # 文件管理
├── gui/                # 图形界面层
│   ├── gui_controller.py # GUI控制器（简化版）
│   ├── gui_view.py      # GUI视图（简化版）
│   └── components/      # GUI组件（完整版）
│       ├── main_window.py    # 主窗口
│       ├── control_panel.py  # 控制面板
│       ├── video_panel.py    # 视频面板
│       ├── config_panel.py   # 配置面板
│       └── log_panel.py      # 日志面板
├── logs/               # 日志目录
├── temp/               # 临时文件目录
├── data/               # 数据目录
└── config/             # 配置目录
```

## 开发说明

### 架构设计
项目采用分层架构设计：
- **基础设施层**: 提供日志、配置、状态管理等基础服务
- **业务逻辑层**: 实现核心业务逻辑和流程控制
- **API接口层**: 封装外部API调用和浏览器操作
- **图形界面层**: 提供用户交互界面

### 扩展开发
如需添加新功能或修改现有功能，请参考：
- `重构文档.md` - 详细的架构说明
- `重构实施指南.md` - 开发指南和最佳实践

### 贡献代码
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看本文档的故障排除部分
2. 检查 [Issues](https://github.com/your-username/hubstudio-uploader/issues) 中是否有类似问题
3. 创建新的 Issue 描述您的问题

## 更新日志

### v1.0.0 (2024-01-XX)
- 🎉 首次发布
- ✨ 支持批量视频上传
- ✨ 图形化用户界面
- ✨ 多平台支持
- ✨ 并发上传功能
- ✨ 模板系统
- ✨ 环境管理

---

**感谢使用 HubStudio 视频上传工具！**