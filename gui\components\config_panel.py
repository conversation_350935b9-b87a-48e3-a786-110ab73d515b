"""
配置面板组件 - 负责HubStudio API和系统配置
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, Any, Optional, Callable


class ConfigPanel:
    """配置面板组件"""
    
    def __init__(self, parent, event_manager, config_manager):
        self.parent = parent
        self.event_manager = event_manager
        self.config_manager = config_manager
        
        # 界面变量
        self.api_url_var = tk.StringVar(value="http://127.0.0.1:50325")
        self.api_id_var = tk.StringVar()
        self.api_secret_var = tk.StringVar()
        self.environment_type_var = tk.StringVar(value="chrome")
        self.wait_timeout_var = tk.StringVar(value="30")
        self.retry_count_var = tk.StringVar(value="3")
        
        # 状态变量
        self.connection_status = "未连接"
        self.environments_list = []
        self.selected_environment = None
        
        # 控件引用
        self.connection_status_label = None
        self.test_connection_btn = None
        self.refresh_env_btn = None
        self.env_listbox = None
        self.start_env_btn = None
        self.stop_env_btn = None
        
        self.create_widgets()
        self.setup_events()
        self.load_config()
    
    def create_widgets(self):
        """创建配置面板界面"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="系统配置", padding="15")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Notebook来组织不同的配置页面
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # API配置页面
        api_frame = ttk.Frame(self.notebook)
        self.notebook.add(api_frame, text="API配置")
        self._create_api_config_tab(api_frame)
        
        # 环境管理页面
        env_frame = ttk.Frame(self.notebook)
        self.notebook.add(env_frame, text="环境管理")
        self._create_environment_tab(env_frame)
        
        # 系统设置页面
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="系统设置")
        self._create_system_settings_tab(system_frame)
    
    def _create_api_config_tab(self, parent):
        """创建API配置标签页"""
        # API URL设置
        url_frame = ttk.LabelFrame(parent, text="HubStudio API设置", padding="10")
        url_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(url_frame, text="API地址:").grid(row=0, column=0, sticky=tk.W, pady=2)
        api_url_entry = ttk.Entry(url_frame, textvariable=self.api_url_var, width=40)
        api_url_entry.grid(row=0, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        ttk.Label(url_frame, text="API ID:").grid(row=1, column=0, sticky=tk.W, pady=2)
        api_id_entry = ttk.Entry(url_frame, textvariable=self.api_id_var, width=40)
        api_id_entry.grid(row=1, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        ttk.Label(url_frame, text="API Secret:").grid(row=2, column=0, sticky=tk.W, pady=2)
        api_secret_entry = ttk.Entry(url_frame, textvariable=self.api_secret_var, width=40, show="*")
        api_secret_entry.grid(row=2, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        url_frame.grid_columnconfigure(1, weight=1)
        
        # 连接测试区域
        test_frame = ttk.LabelFrame(parent, text="连接测试", padding="10")
        test_frame.pack(fill=tk.X, pady=5)
        
        # 连接状态
        status_frame = ttk.Frame(test_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(status_frame, text="连接状态:").pack(side=tk.LEFT)
        self.connection_status_label = ttk.Label(status_frame, text=self.connection_status, 
                                               foreground="red", font=("Arial", 9, "bold"))
        self.connection_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 测试按钮
        button_frame = ttk.Frame(test_frame)
        button_frame.pack(fill=tk.X)
        
        self.test_connection_btn = ttk.Button(
            button_frame,
            text="测试连接",
            command=self._test_connection
        )
        self.test_connection_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        save_config_btn = ttk.Button(
            button_frame,
            text="保存配置",
            command=self._save_config
        )
        save_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        load_config_btn = ttk.Button(
            button_frame,
            text="重新加载",
            command=self.load_config
        )
        load_config_btn.pack(side=tk.LEFT)
    
    def _create_environment_tab(self, parent):
        """创建环境管理标签页"""
        # 环境类型设置
        type_frame = ttk.LabelFrame(parent, text="环境类型", padding="10")
        type_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(type_frame, text="浏览器类型:").pack(side=tk.LEFT)
        env_type_combo = ttk.Combobox(type_frame, textvariable=self.environment_type_var, 
                                     width=15, state="readonly")
        env_type_combo['values'] = ('chrome', 'firefox', 'edge')
        env_type_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 环境列表
        list_frame = ttk.LabelFrame(parent, text="环境列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 环境列表控件
        env_list_frame = ttk.Frame(list_frame)
        env_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview显示环境信息
        columns = ('环境ID', '名称', '状态', '类型')
        self.env_tree = ttk.Treeview(env_list_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        self.env_tree.heading('环境ID', text='环境ID')
        self.env_tree.heading('名称', text='名称')
        self.env_tree.heading('状态', text='状态')
        self.env_tree.heading('类型', text='类型')
        
        # 设置列宽
        self.env_tree.column('环境ID', width=100)
        self.env_tree.column('名称', width=150)
        self.env_tree.column('状态', width=80)
        self.env_tree.column('类型', width=80)
        
        # 添加滚动条
        env_scrollbar = ttk.Scrollbar(env_list_frame, orient=tk.VERTICAL, command=self.env_tree.yview)
        self.env_tree.configure(yscrollcommand=env_scrollbar.set)
        
        self.env_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        env_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 环境操作按钮
        env_button_frame = ttk.Frame(list_frame)
        env_button_frame.pack(fill=tk.X)
        
        self.refresh_env_btn = ttk.Button(
            env_button_frame,
            text="刷新列表",
            command=self._refresh_environments
        )
        self.refresh_env_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.start_env_btn = ttk.Button(
            env_button_frame,
            text="启动环境",
            command=self._start_environment
        )
        self.start_env_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_env_btn = ttk.Button(
            env_button_frame,
            text="停止环境",
            command=self._stop_environment
        )
        self.stop_env_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 环境信息显示
        info_label = ttk.Label(env_button_frame, text="双击环境查看详细信息", 
                              font=("Arial", 8), foreground="gray")
        info_label.pack(side=tk.RIGHT)
        
        # 绑定选择事件
        self.env_tree.bind('<<TreeviewSelect>>', self._on_environment_select)
        self.env_tree.bind('<Double-1>', self._on_environment_double_click)
    
    def _create_system_settings_tab(self, parent):
        """创建系统设置标签页"""
        # 超时设置
        timeout_frame = ttk.LabelFrame(parent, text="超时设置", padding="10")
        timeout_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(timeout_frame, text="等待超时:").grid(row=0, column=0, sticky=tk.W, pady=2)
        timeout_spinbox = ttk.Spinbox(timeout_frame, from_=5, to=300, width=10,
                                     textvariable=self.wait_timeout_var)
        timeout_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 5), pady=2)
        ttk.Label(timeout_frame, text="秒").grid(row=0, column=2, sticky=tk.W, pady=2)
        
        ttk.Label(timeout_frame, text="重试次数:").grid(row=1, column=0, sticky=tk.W, pady=2)
        retry_spinbox = ttk.Spinbox(timeout_frame, from_=1, to=10, width=10,
                                   textvariable=self.retry_count_var)
        retry_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(10, 5), pady=2)
        ttk.Label(timeout_frame, text="次").grid(row=1, column=2, sticky=tk.W, pady=2)
        
        # 日志设置
        log_frame = ttk.LabelFrame(parent, text="日志设置", padding="10")
        log_frame.pack(fill=tk.X, pady=5)
        
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill=tk.X)
        
        clear_log_btn = ttk.Button(
            log_button_frame,
            text="清理日志",
            command=self._clear_logs
        )
        clear_log_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        export_log_btn = ttk.Button(
            log_button_frame,
            text="导出日志",
            command=self._export_logs
        )
        export_log_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 系统操作
        system_frame = ttk.LabelFrame(parent, text="系统操作", padding="10")
        system_frame.pack(fill=tk.X, pady=5)
        
        system_button_frame = ttk.Frame(system_frame)
        system_button_frame.pack(fill=tk.X)
        
        reset_config_btn = ttk.Button(
            system_button_frame,
            text="重置配置",
            command=self._reset_config
        )
        reset_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        backup_config_btn = ttk.Button(
            system_button_frame,
            text="备份配置",
            command=self._backup_config
        )
        backup_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        restore_config_btn = ttk.Button(
            system_button_frame,
            text="恢复配置",
            command=self._restore_config
        )
        restore_config_btn.pack(side=tk.LEFT)
    
    def setup_events(self):
        """设置事件绑定"""
        if self.event_manager:
            # 订阅配置相关事件
            self.event_manager.subscribe('config_updated', self._on_config_updated)
            self.event_manager.subscribe('connection_status_changed', self._on_connection_status_changed)
            self.event_manager.subscribe('environments_updated', self._on_environments_updated)
            
            # 订阅API测试事件
            self.event_manager.subscribe('api_test_completed', self._on_api_test_completed)
    
    def load_config(self):
        """加载配置"""
        try:
            if self.config_manager:
                # 加载API配置
                self.api_url_var.set(self.config_manager.get('BROWSER', 'api_url', 'http://127.0.0.1:50325'))
                self.api_id_var.set(self.config_manager.get('HUBSTUDIO', 'api_id', ''))
                self.api_secret_var.set(self.config_manager.get('HUBSTUDIO', 'api_secret', ''))
                self.environment_type_var.set(self.config_manager.get('HUBSTUDIO', 'environment_type', 'chrome'))
                
                # 加载系统设置
                self.wait_timeout_var.set(self.config_manager.get('BROWSER', 'wait_timeout', '30'))
                self.retry_count_var.set(self.config_manager.get('BROWSER', 'retry_count', '3'))
                
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def _save_config(self):
        """保存配置"""
        try:
            if self.config_manager:
                # 保存API配置
                self.config_manager.set('BROWSER', 'api_url', self.api_url_var.get())
                self.config_manager.set('HUBSTUDIO', 'api_id', self.api_id_var.get())
                self.config_manager.set('HUBSTUDIO', 'api_secret', self.api_secret_var.get())
                self.config_manager.set('HUBSTUDIO', 'environment_type', self.environment_type_var.get())
                
                # 保存系统设置
                self.config_manager.set('BROWSER', 'wait_timeout', self.wait_timeout_var.get())
                self.config_manager.set('BROWSER', 'retry_count', self.retry_count_var.get())
                
                # 保存到文件
                self.config_manager.save()
                
                messagebox.showinfo("完成", "配置保存成功")
                
                # 发布配置更新事件
                if self.event_manager:
                    self.event_manager.publish('config_saved', self._get_current_config())
                    
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _test_connection(self):
        """测试API连接"""
        try:
            self.test_connection_btn.config(state=tk.DISABLED, text="测试中...")
            self._update_connection_status("测试中...")
            
            # 在后台线程中执行测试
            def test_thread():
                try:
                    config = self._get_current_config()
                    
                    if self.event_manager:
                        self.event_manager.publish('api_test_requested', config)
                    
                except Exception as e:
                    self._on_api_test_completed({'success': False, 'message': str(e)})
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            self._on_api_test_completed({'success': False, 'message': str(e)})
    
    def _refresh_environments(self):
        """刷新环境列表"""
        try:
            self.refresh_env_btn.config(state=tk.DISABLED, text="刷新中...")
            
            # 在后台线程中刷新
            def refresh_thread():
                try:
                    if self.event_manager:
                        self.event_manager.publish('refresh_environments_requested', {})
                except Exception as e:
                    print(f"刷新环境失败: {e}")
                finally:
                    # 恢复按钮状态
                    self.refresh_env_btn.config(state=tk.NORMAL, text="刷新列表")
            
            threading.Thread(target=refresh_thread, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新环境列表失败: {e}")
            self.refresh_env_btn.config(state=tk.NORMAL, text="刷新列表")
    
    def _start_environment(self):
        """启动选中的环境"""
        try:
            selected_items = self.env_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要启动的环境")
                return
            
            env_id = self.env_tree.item(selected_items[0])['values'][0]
            
            if self.event_manager:
                self.event_manager.publish('start_environment_requested', {'environment_id': env_id})
                
        except Exception as e:
            messagebox.showerror("错误", f"启动环境失败: {e}")
    
    def _stop_environment(self):
        """停止选中的环境"""
        try:
            selected_items = self.env_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要停止的环境")
                return
            
            env_id = self.env_tree.item(selected_items[0])['values'][0]
            
            if messagebox.askyesno("确认", f"确定要停止环境 {env_id} 吗？"):
                if self.event_manager:
                    self.event_manager.publish('stop_environment_requested', {'environment_id': env_id})
                    
        except Exception as e:
            messagebox.showerror("错误", f"停止环境失败: {e}")
    
    def _on_environment_select(self, event):
        """环境选择事件"""
        try:
            selected_items = self.env_tree.selection()
            if selected_items:
                env_data = self.env_tree.item(selected_items[0])['values']
                self.selected_environment = {
                    'id': env_data[0],
                    'name': env_data[1],
                    'status': env_data[2],
                    'type': env_data[3]
                }
                
                # 更新按钮状态
                is_running = env_data[2] == '运行中'
                self.start_env_btn.config(state=tk.DISABLED if is_running else tk.NORMAL)
                self.stop_env_btn.config(state=tk.NORMAL if is_running else tk.DISABLED)
            else:
                self.selected_environment = None
                self.start_env_btn.config(state=tk.DISABLED)
                self.stop_env_btn.config(state=tk.DISABLED)
                
        except Exception as e:
            print(f"环境选择事件处理失败: {e}")
    
    def _on_environment_double_click(self, event):
        """环境双击事件"""
        try:
            if self.selected_environment:
                self._show_environment_info(self.selected_environment)
        except Exception as e:
            print(f"环境双击事件处理失败: {e}")
    
    def _show_environment_info(self, env_info):
        """显示环境详细信息"""
        try:
            info_window = tk.Toplevel(self.parent)
            info_window.title(f"环境信息 - {env_info['name']}")
            info_window.geometry("400x300")
            info_window.resizable(False, False)
            
            # 环境信息内容
            info_text = f"""环境ID: {env_info['id']}
环境名称: {env_info['name']}
运行状态: {env_info['status']}
浏览器类型: {env_info['type']}
"""
            
            text_widget = tk.Text(info_window, wrap=tk.WORD, padx=10, pady=10)
            text_widget.pack(fill=tk.BOTH, expand=True)
            text_widget.insert(tk.END, info_text)
            text_widget.config(state=tk.DISABLED)
            
            # 关闭按钮
            close_btn = ttk.Button(info_window, text="关闭", command=info_window.destroy)
            close_btn.pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", f"显示环境信息失败: {e}")
    
    def _clear_logs(self):
        """清理日志"""
        try:
            if messagebox.askyesno("确认", "确定要清理所有日志吗？"):
                if self.event_manager:
                    self.event_manager.publish('clear_logs_requested', {})
                messagebox.showinfo("完成", "日志清理完成")
        except Exception as e:
            messagebox.showerror("错误", f"清理日志失败: {e}")
    
    def _export_logs(self):
        """导出日志"""
        try:
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="导出日志",
                defaultextension=".log",
                filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                if self.event_manager:
                    self.event_manager.publish('export_logs_requested', {'file_path': file_path})
                messagebox.showinfo("完成", f"日志已导出到: {file_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")
    
    def _reset_config(self):
        """重置配置"""
        try:
            if messagebox.askyesno("确认", "确定要重置所有配置为默认值吗？这将清除所有自定义设置。"):
                if self.config_manager:
                    self.config_manager.reset_to_defaults()
                    self.load_config()
                    messagebox.showinfo("完成", "配置已重置为默认值")
        except Exception as e:
            messagebox.showerror("错误", f"重置配置失败: {e}")
    
    def _backup_config(self):
        """备份配置"""
        try:
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="备份配置",
                defaultextension=".ini",
                filetypes=[("配置文件", "*.ini"), ("所有文件", "*.*")]
            )
            
            if file_path:
                if self.config_manager:
                    self.config_manager.backup_to_file(file_path)
                    messagebox.showinfo("完成", f"配置已备份到: {file_path}")
                    
        except Exception as e:
            messagebox.showerror("错误", f"备份配置失败: {e}")
    
    def _restore_config(self):
        """恢复配置"""
        try:
            from tkinter import filedialog
            file_path = filedialog.askopenfilename(
                title="恢复配置",
                filetypes=[("配置文件", "*.ini"), ("所有文件", "*.*")]
            )
            
            if file_path:
                if messagebox.askyesno("确认", "确定要从备份文件恢复配置吗？这将覆盖当前配置。"):
                    if self.config_manager:
                        self.config_manager.restore_from_file(file_path)
                        self.load_config()
                        messagebox.showinfo("完成", "配置恢复成功")
                        
        except Exception as e:
            messagebox.showerror("错误", f"恢复配置失败: {e}")
    
    # ==================== 事件处理方法 ====================
    
    def _on_config_updated(self, config_data):
        """配置更新事件"""
        self.load_config()
    
    def _on_connection_status_changed(self, status_data):
        """连接状态变化事件"""
        status = status_data.get('status', '未知')
        self._update_connection_status(status)
    
    def _on_environments_updated(self, env_data):
        """环境列表更新事件"""
        try:
            environments = env_data.get('environments', [])
            self._update_environment_list(environments)
        except Exception as e:
            print(f"更新环境列表失败: {e}")
    
    def _on_api_test_completed(self, result):
        """API测试完成事件"""
        try:
            success = result.get('success', False)
            message = result.get('message', '')
            
            if success:
                self._update_connection_status("已连接")
                messagebox.showinfo("连接成功", "API连接测试成功！")
            else:
                self._update_connection_status("连接失败")
                messagebox.showerror("连接失败", f"API连接测试失败：{message}")
                
        except Exception as e:
            self._update_connection_status("测试错误")
            messagebox.showerror("测试错误", f"连接测试出现错误：{e}")
        finally:
            self.test_connection_btn.config(state=tk.NORMAL, text="测试连接")
    
    # ==================== 界面更新方法 ====================
    
    def _update_connection_status(self, status: str):
        """更新连接状态显示"""
        try:
            self.connection_status = status
            self.connection_status_label.config(text=status)
            
            # 设置状态颜色
            color_map = {
                "已连接": "green",
                "连接失败": "red",
                "测试中...": "orange",
                "未连接": "gray"
            }
            color = color_map.get(status, "black")
            self.connection_status_label.config(foreground=color)
            
        except Exception as e:
            print(f"更新连接状态失败: {e}")
    
    def _update_environment_list(self, environments):
        """更新环境列表显示"""
        try:
            # 清空现有列表
            for item in self.env_tree.get_children():
                self.env_tree.delete(item)
            
            # 添加新的环境信息
            for env in environments:
                env_id = env.get('environment_id', '')
                name = env.get('name', '')
                status = '运行中' if env.get('status') == 'running' else '已停止'
                env_type = env.get('type', '')
                
                self.env_tree.insert('', tk.END, values=(env_id, name, status, env_type))
            
            self.environments_list = environments
            
        except Exception as e:
            print(f"更新环境列表失败: {e}")
    
    # ==================== 公共接口 ====================
    
    def _get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'api_url': self.api_url_var.get(),
            'api_id': self.api_id_var.get(),
            'api_secret': self.api_secret_var.get(),
            'environment_type': self.environment_type_var.get(),
            'wait_timeout': int(self.wait_timeout_var.get()),
            'retry_count': int(self.retry_count_var.get())
        }
    
    def get_api_config(self) -> Dict[str, str]:
        """获取API配置"""
        return {
            'api_url': self.api_url_var.get(),
            'api_id': self.api_id_var.get(),
            'api_secret': self.api_secret_var.get()
        }
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return {
            'environment_type': self.environment_type_var.get(),
            'wait_timeout': int(self.wait_timeout_var.get()),
            'retry_count': int(self.retry_count_var.get())
        }
    
    def set_connection_status(self, status: str):
        """设置连接状态（公共接口）"""
        self._update_connection_status(status)
    
    def get_selected_environment(self) -> Optional[Dict[str, str]]:
        """获取选中的环境"""
        return self.selected_environment
    
    def refresh_environments(self):
        """刷新环境列表（公共接口）"""
        self._refresh_environments()
    
    def update_environment_list(self, environments):
        """更新环境列表（公共接口）"""
        self._update_environment_list(environments)
