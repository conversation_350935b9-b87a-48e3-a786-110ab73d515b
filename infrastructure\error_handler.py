"""
错误处理器 - 提供统一的错误处理和恢复机制
"""

import traceback
import threading
import time
from typing import Any, Dict, List, Callable, Optional, Type
from dataclasses import dataclass, field
from enum import Enum
import sys
import os


class ErrorLevel(Enum):
    """错误级别"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    NETWORK = "network"
    FILE_IO = "file_io"
    API = "api"
    BROWSER = "browser"
    VALIDATION = "validation"
    CONFIGURATION = "configuration"
    SYSTEM = "system"
    USER_INPUT = "user_input"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    level: ErrorLevel
    category: ErrorCategory
    message: str
    exception: Exception = None
    context: str = ""
    timestamp: float = field(default_factory=time.time)
    stack_trace: str = ""
    metadata: Dict = field(default_factory=dict)
    resolved: bool = False
    resolution_message: str = ""


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, logger=None, state_manager=None):
        self.logger = logger
        self.state_manager = state_manager
        self._error_history = []
        self._error_handlers = {}  # category -> handler function
        self._recovery_strategies = {}  # category -> recovery function
        self._lock = threading.RLock()
        self._error_counter = 0
        self._max_history = 1000
        
        # 注册默认错误处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认错误处理器"""
        # 网络错误处理
        self.register_handler(ErrorCategory.NETWORK, self._handle_network_error)
        
        # 文件IO错误处理
        self.register_handler(ErrorCategory.FILE_IO, self._handle_file_io_error)
        
        # API错误处理
        self.register_handler(ErrorCategory.API, self._handle_api_error)
        
        # 浏览器错误处理
        self.register_handler(ErrorCategory.BROWSER, self._handle_browser_error)
        
        # 配置错误处理
        self.register_handler(ErrorCategory.CONFIGURATION, self._handle_config_error)
    
    def handle_error(self, exception: Exception, context: str = "", 
                    category: ErrorCategory = ErrorCategory.UNKNOWN,
                    level: ErrorLevel = ErrorLevel.ERROR,
                    metadata: Dict = None) -> ErrorInfo:
        """处理错误"""
        with self._lock:
            self._error_counter += 1
            error_id = f"ERR_{self._error_counter:06d}"
            
            # 创建错误信息
            error_info = ErrorInfo(
                error_id=error_id,
                level=level,
                category=category,
                message=str(exception),
                exception=exception,
                context=context,
                stack_trace=traceback.format_exc(),
                metadata=metadata or {}
            )
            
            # 添加到历史记录
            self._add_to_history(error_info)
            
            # 记录日志
            self._log_error(error_info)
            
            # 更新状态
            self._update_state(error_info)
            
            # 执行错误处理
            self._execute_handler(error_info)
            
            return error_info
    
    def _add_to_history(self, error_info: ErrorInfo):
        """添加到错误历史"""
        self._error_history.append(error_info)
        
        # 限制历史记录数量
        if len(self._error_history) > self._max_history:
            self._error_history = self._error_history[-self._max_history:]
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        if self.logger:
            log_message = f"[{error_info.error_id}] {error_info.context}: {error_info.message}"
            
            if error_info.level == ErrorLevel.DEBUG:
                self.logger.debug(log_message)
            elif error_info.level == ErrorLevel.INFO:
                self.logger.info(log_message)
            elif error_info.level == ErrorLevel.WARNING:
                self.logger.warning(log_message)
            elif error_info.level == ErrorLevel.ERROR:
                self.logger.error(log_message)
            elif error_info.level == ErrorLevel.CRITICAL:
                self.logger.critical(log_message)
                # 关键错误时记录完整堆栈
                self.logger.critical(f"堆栈跟踪:\n{error_info.stack_trace}")
    
    def _update_state(self, error_info: ErrorInfo):
        """更新状态管理器"""
        if self.state_manager:
            # 更新错误计数
            self.state_manager.increment('error_count')
            
            # 更新最后错误信息
            self.state_manager.set('last_error', {
                'id': error_info.error_id,
                'message': error_info.message,
                'category': error_info.category.value,
                'level': error_info.level.value,
                'timestamp': error_info.timestamp
            })
            
            # 更新分类错误计数
            category_key = f'error_count_{error_info.category.value}'
            self.state_manager.increment(category_key)
    
    def _execute_handler(self, error_info: ErrorInfo):
        """执行错误处理器"""
        try:
            handler = self._error_handlers.get(error_info.category)
            if handler:
                handler(error_info)
            else:
                # 使用默认处理器
                self._default_error_handler(error_info)
        except Exception as e:
            # 处理器本身出错时的兜底处理
            print(f"错误处理器执行失败: {e}")
            if self.logger:
                self.logger.error(f"错误处理器执行失败: {e}")
    
    def register_handler(self, category: ErrorCategory, 
                        handler: Callable[[ErrorInfo], None]):
        """注册错误处理器"""
        self._error_handlers[category] = handler
    
    def register_recovery_strategy(self, category: ErrorCategory,
                                 strategy: Callable[[ErrorInfo], bool]):
        """注册恢复策略"""
        self._recovery_strategies[category] = strategy
    
    def attempt_recovery(self, error_info: ErrorInfo) -> bool:
        """尝试错误恢复"""
        try:
            strategy = self._recovery_strategies.get(error_info.category)
            if strategy:
                success = strategy(error_info)
                if success:
                    error_info.resolved = True
                    error_info.resolution_message = "自动恢复成功"
                    self._log_recovery(error_info)
                return success
            return False
        except Exception as e:
            if self.logger:
                self.logger.error(f"恢复策略执行失败: {e}")
            return False
    
    def _log_recovery(self, error_info: ErrorInfo):
        """记录恢复日志"""
        if self.logger:
            self.logger.info(f"[{error_info.error_id}] 错误恢复成功: {error_info.resolution_message}")
    
    # 默认错误处理器
    def _default_error_handler(self, error_info: ErrorInfo):
        """默认错误处理器"""
        # 尝试自动恢复
        if self.attempt_recovery(error_info):
            return
        
        # 根据错误级别采取不同行动
        if error_info.level == ErrorLevel.CRITICAL:
            self._handle_critical_error(error_info)
        elif error_info.level == ErrorLevel.ERROR:
            self._handle_general_error(error_info)
    
    def _handle_critical_error(self, error_info: ErrorInfo):
        """处理关键错误"""
        if self.logger:
            self.logger.critical(f"关键错误发生: {error_info.message}")
        
        # 更新状态为错误状态
        if self.state_manager:
            self.state_manager.set('app_error_state', True)
            self.state_manager.set('critical_error', error_info.message)
    
    def _handle_general_error(self, error_info: ErrorInfo):
        """处理一般错误"""
        # 记录错误但不中断程序执行
        pass
    
    # 具体错误类型处理器
    def _handle_network_error(self, error_info: ErrorInfo):
        """处理网络错误"""
        if self.logger:
            self.logger.warning(f"网络错误: {error_info.message}")
        
        # 更新API连接状态
        if self.state_manager:
            self.state_manager.set('api_connected', False)
            self.state_manager.increment('api_error_count')
    
    def _handle_file_io_error(self, error_info: ErrorInfo):
        """处理文件IO错误"""
        if self.logger:
            self.logger.error(f"文件操作错误: {error_info.message}")
        
        # 检查是否是权限问题
        if "Permission denied" in error_info.message or "权限" in error_info.message:
            error_info.resolution_message = "请检查文件权限或以管理员身份运行"
    
    def _handle_api_error(self, error_info: ErrorInfo):
        """处理API错误"""
        if self.logger:
            self.logger.error(f"API错误: {error_info.message}")
        
        # 更新API状态
        if self.state_manager:
            self.state_manager.set('api_connected', False)
            self.state_manager.set('api_last_error', error_info.message)
    
    def _handle_browser_error(self, error_info: ErrorInfo):
        """处理浏览器错误"""
        if self.logger:
            self.logger.error(f"浏览器错误: {error_info.message}")
        
        # 标记需要重启浏览器
        if self.state_manager:
            self.state_manager.set('browser_restart_needed', True)
    
    def _handle_config_error(self, error_info: ErrorInfo):
        """处理配置错误"""
        if self.logger:
            self.logger.error(f"配置错误: {error_info.message}")
        
        # 更新配置状态
        if self.state_manager:
            self.state_manager.set('config_valid', False)
            self.state_manager.set('config_error', error_info.message)
    
    # 查询方法
    def get_error_history(self, category: ErrorCategory = None, 
                         level: ErrorLevel = None, 
                         limit: int = None) -> List[ErrorInfo]:
        """获取错误历史"""
        with self._lock:
            history = self._error_history.copy()
            
            # 按分类过滤
            if category:
                history = [e for e in history if e.category == category]
            
            # 按级别过滤
            if level:
                history = [e for e in history if e.level == level]
            
            # 限制数量
            if limit:
                history = history[-limit:]
            
            return history
    
    def get_error_statistics(self) -> Dict:
        """获取错误统计信息"""
        with self._lock:
            stats = {
                'total_errors': len(self._error_history),
                'resolved_errors': len([e for e in self._error_history if e.resolved]),
                'by_category': {},
                'by_level': {},
                'recent_errors': len([e for e in self._error_history 
                                    if time.time() - e.timestamp < 3600])  # 最近1小时
            }
            
            # 按分类统计
            for category in ErrorCategory:
                count = len([e for e in self._error_history if e.category == category])
                if count > 0:
                    stats['by_category'][category.value] = count
            
            # 按级别统计
            for level in ErrorLevel:
                count = len([e for e in self._error_history if e.level == level])
                if count > 0:
                    stats['by_level'][level.value] = count
            
            return stats
    
    def clear_history(self, older_than_hours: int = None):
        """清空错误历史"""
        with self._lock:
            if older_than_hours:
                cutoff_time = time.time() - (older_than_hours * 3600)
                self._error_history = [e for e in self._error_history 
                                     if e.timestamp > cutoff_time]
            else:
                self._error_history.clear()
    
    def export_errors(self, format: str = "json") -> str:
        """导出错误信息"""
        with self._lock:
            if format == "json":
                import json
                data = []
                for error in self._error_history:
                    data.append({
                        'error_id': error.error_id,
                        'level': error.level.value,
                        'category': error.category.value,
                        'message': error.message,
                        'context': error.context,
                        'timestamp': error.timestamp,
                        'resolved': error.resolved,
                        'resolution_message': error.resolution_message
                    })
                return json.dumps(data, ensure_ascii=False, indent=2)
            else:
                # 文本格式
                lines = []
                for error in self._error_history:
                    lines.append(f"[{error.error_id}] {error.level.value.upper()} - {error.category.value}")
                    lines.append(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(error.timestamp))}")
                    lines.append(f"上下文: {error.context}")
                    lines.append(f"消息: {error.message}")
                    if error.resolved:
                        lines.append(f"解决方案: {error.resolution_message}")
                    lines.append("-" * 50)
                return "\n".join(lines)
    
    # 便捷方法
    def handle_exception(self, context: str = "", 
                        category: ErrorCategory = ErrorCategory.UNKNOWN,
                        level: ErrorLevel = ErrorLevel.ERROR) -> ErrorInfo:
        """处理当前异常"""
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_value:
            return self.handle_error(exc_value, context, category, level)
        return None
    
    def create_user_friendly_message(self, error_info: ErrorInfo) -> str:
        """创建用户友好的错误消息"""
        base_message = error_info.message
        
        # 根据错误类型提供更友好的消息
        if error_info.category == ErrorCategory.NETWORK:
            return f"网络连接出现问题: {base_message}\n请检查网络连接或稍后重试。"
        elif error_info.category == ErrorCategory.FILE_IO:
            return f"文件操作失败: {base_message}\n请检查文件路径和权限。"
        elif error_info.category == ErrorCategory.API:
            return f"API调用失败: {base_message}\n请检查API配置或联系技术支持。"
        elif error_info.category == ErrorCategory.BROWSER:
            return f"浏览器操作失败: {base_message}\n请尝试重启浏览器或检查环境配置。"
        elif error_info.category == ErrorCategory.CONFIGURATION:
            return f"配置错误: {base_message}\n请检查配置文件设置。"
        else:
            return f"操作失败: {base_message}\n如果问题持续存在，请联系技术支持。"