"""
主题管理器 - 管理GUI的主题和样式
"""

import customtkinter as ctk
from typing import Dict, Any, Optional
from enum import Enum


class ThemeManager:
    """主题管理器 - 固定深色模式"""

    def __init__(self):
        # 固定为深色模式
        self.current_mode = "dark"

        # 优化的深色主题颜色配置
        self.colors = {
            'primary': '#4A90E2',           # 主要蓝色
            'primary_hover': '#357ABD',     # 主要蓝色悬停
            'secondary': '#2D3748',         # 次要灰色
            'background': '#1A202C',        # 背景色
            'surface': '#2D3748',           # 表面色
            'card': '#374151',              # 卡片背景
            'text': '#FFFFFF',              # 主要文字 - 统一白色
            'text_secondary': '#E2E8F0',    # 次要文字 - 浅灰白色
            'border': '#4A5568',            # 边框色
            'success': '#48BB78',           # 成功绿色
            'warning': '#ED8936',           # 警告橙色
            'error': '#F56565',             # 错误红色
            'info': '#4299E1',              # 信息蓝色
            'accent': '#9F7AEA',            # 强调紫色
            'hover': '#4A5568'              # 悬停色
        }
        
        # 优化的字体配置
        self.fonts = {
            'default': ('Microsoft YaHei UI', 12),      # 微软雅黑，更好的中文显示
            'heading': ('Microsoft YaHei UI', 18, 'bold'),
            'subheading': ('Microsoft YaHei UI', 14, 'bold'),
            'small': ('Microsoft YaHei UI', 10),
            'large': ('Microsoft YaHei UI', 16),
            'monospace': ('Consolas', 11)
        }

        # 优化的间距配置
        self.spacing = {
            'xs': 4,
            'sm': 8,
            'md': 12,
            'lg': 20,
            'xl': 28
        }

        # 圆角配置
        self.corner_radius = {
            'small': 6,
            'medium': 10,
            'large': 14
        }

        # 初始化CustomTkinter主题
        self._apply_theme()

    def _apply_theme(self):
        """应用深色主题设置"""
        # 固定设置为深色模式
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

    def get_color(self, color_name: str) -> str:
        """获取颜色值"""
        return self.colors.get(color_name, '#FFFFFF')
    
    def get_font(self, font_name: str = 'default') -> tuple:
        """获取字体配置"""
        return self.fonts.get(font_name, self.fonts['default'])
    
    def get_spacing(self, size: str = 'md') -> int:
        """获取间距值"""
        return self.spacing.get(size, 16)
    
    def get_corner_radius(self, size: str = 'medium') -> int:
        """获取圆角值"""
        return self.corner_radius.get(size, 8)
    
    def create_button_style(self, variant: str = 'primary') -> Dict[str, Any]:
        """创建按钮样式"""
        styles = {
            'primary': {
                'fg_color': self.get_color('primary'),
                'hover_color': self.get_color('primary_hover'),
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 0
            },
            'secondary': {
                'fg_color': self.get_color('secondary'),
                'hover_color': self.get_color('hover'),
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 1,
                'border_color': self.get_color('border')
            },
            'success': {
                'fg_color': self.get_color('success'),
                'hover_color': '#38A169',
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 0
            },
            'warning': {
                'fg_color': self.get_color('warning'),
                'hover_color': '#DD6B20',
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 0
            },
            'error': {
                'fg_color': self.get_color('error'),
                'hover_color': '#E53E3E',
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 0
            },
            'accent': {
                'fg_color': self.get_color('accent'),
                'hover_color': '#805AD5',
                'text_color': self.get_color('text'),
                'corner_radius': self.get_corner_radius('medium'),
                'font': self.get_font('default'),
                'border_width': 0
            }
        }

        return styles.get(variant, styles['primary'])
    
    def create_frame_style(self, variant: str = 'default') -> Dict[str, Any]:
        """创建框架样式"""
        styles = {
            'default': {
                'fg_color': self.get_color('surface'),
                'corner_radius': self.get_corner_radius('medium'),
                'border_width': 1,
                'border_color': self.get_color('border')
            },
            'card': {
                'fg_color': self.get_color('card'),
                'corner_radius': self.get_corner_radius('large'),
                'border_width': 0
            },
            'panel': {
                'fg_color': self.get_color('background'),
                'corner_radius': self.get_corner_radius('medium'),
                'border_width': 1,
                'border_color': self.get_color('border')
            },
            'transparent': {
                'fg_color': 'transparent',
                'corner_radius': 0,
                'border_width': 0
            }
        }

        return styles.get(variant, styles['default'])
    
    def create_label_style(self, variant: str = 'default') -> Dict[str, Any]:
        """创建标签样式 - 统一白色文字"""
        styles = {
            'default': {
                'text_color': self.get_color('text'),  # 白色
                'font': self.get_font('default')
            },
            'heading': {
                'text_color': self.get_color('text'),  # 白色
                'font': self.get_font('heading')
            },
            'subheading': {
                'text_color': self.get_color('text'),  # 白色
                'font': self.get_font('subheading')
            },
            'secondary': {
                'text_color': self.get_color('text_secondary'),  # 浅灰白色
                'font': self.get_font('default')
            },
            'small': {
                'text_color': self.get_color('text_secondary'),  # 浅灰白色
                'font': self.get_font('small')
            },
            'large': {
                'text_color': self.get_color('text'),  # 白色
                'font': self.get_font('large')
            },
            'success': {
                'text_color': self.get_color('text'),  # 统一白色
                'font': self.get_font('default')
            },
            'warning': {
                'text_color': self.get_color('text'),  # 统一白色
                'font': self.get_font('default')
            },
            'error': {
                'text_color': self.get_color('text'),  # 统一白色
                'font': self.get_font('default')
            },
            'info': {
                'text_color': self.get_color('text'),  # 统一白色
                'font': self.get_font('default')
            }
        }

        return styles.get(variant, styles['default'])
    
    def create_entry_style(self) -> Dict[str, Any]:
        """创建输入框样式"""
        return {
            'fg_color': self.get_color('background'),
            'border_color': self.get_color('border'),
            'text_color': self.get_color('text'),
            'placeholder_text_color': self.get_color('text_secondary'),
            'corner_radius': self.get_corner_radius('medium'),
            'border_width': 1,
            'font': self.get_font('default')
        }
    
    def create_progressbar_style(self) -> Dict[str, Any]:
        """创建进度条样式"""
        return {
            'fg_color': self.get_color('secondary'),
            'progress_color': self.get_color('primary'),
            'corner_radius': self.get_corner_radius('small'),
            'border_width': 0
        }
    
    def create_scrollbar_style(self) -> Dict[str, Any]:
        """创建滚动条样式"""
        return {
            'fg_color': self.get_color('secondary'),
            'button_color': self.get_color('border'),
            'button_hover_color': self.get_color('primary'),
            'corner_radius': self.get_corner_radius('small')
        }
    
    def get_status_color(self, status: str) -> str:
        """根据状态获取颜色 - 仅用于状态指示器"""
        status_colors = {
            'success': self.get_color('success'),
            'completed': self.get_color('success'),
            'online': self.get_color('success'),
            'active': self.get_color('success'),

            'warning': self.get_color('warning'),
            'pending': self.get_color('warning'),
            'queued': self.get_color('warning'),
            'busy': self.get_color('warning'),

            'error': self.get_color('error'),
            'failed': self.get_color('error'),
            'offline': self.get_color('error'),
            'cancelled': self.get_color('error'),

            'info': self.get_color('info'),
            'uploading': self.get_color('info'),
            'processing': self.get_color('info'),

            'default': self.get_color('text_secondary'),
            'inactive': self.get_color('text_secondary'),
            'unknown': self.get_color('text_secondary')
        }

        return status_colors.get(status.lower(), status_colors['default'])

    def get_status_text_color(self, status: str) -> str:
        """获取状态文字颜色 - 统一白色"""
        return self.get_color('text')
    
    def create_tooltip_style(self) -> Dict[str, Any]:
        """创建工具提示样式"""
        return {
            'fg_color': self.get_color('surface'),
            'text_color': self.get_color('text'),
            'corner_radius': self.get_corner_radius('small'),
            'font': self.get_font('small'),
            'border_width': 1,
            'border_color': self.get_color('border')
        }
    
    def apply_widget_style(self, widget, style_dict: Dict[str, Any]):
        """应用样式到控件"""
        for key, value in style_dict.items():
            if hasattr(widget, f'configure'):
                try:
                    widget.configure(**{key: value})
                except Exception:
                    pass  # 忽略不支持的属性
    
    def get_theme_config(self) -> Dict[str, Any]:
        """获取完整主题配置"""
        return {
            'mode': self.current_mode.value,
            'scheme': self.current_scheme.value,
            'colors': self.colors,
            'fonts': self.fonts,
            'spacing': self.spacing,
            'corner_radius': self.corner_radius
        }
    
    def load_theme_config(self, config: Dict[str, Any]):
        """加载主题配置"""
        try:
            if 'mode' in config:
                self.current_mode = ThemeMode(config['mode'])
            
            if 'scheme' in config:
                self.current_scheme = ColorScheme(config['scheme'])
            
            if 'colors' in config:
                self.colors.update(config['colors'])
            
            if 'fonts' in config:
                self.fonts.update(config['fonts'])
            
            if 'spacing' in config:
                self.spacing.update(config['spacing'])
            
            if 'corner_radius' in config:
                self.corner_radius.update(config['corner_radius'])
            
            self._apply_theme()
            
        except Exception as e:
            print(f"加载主题配置失败: {e}")


# 全局主题管理器实例
theme_manager = ThemeManager()
