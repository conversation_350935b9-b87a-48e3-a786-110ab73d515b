"""
浏览器管理器 - 管理Selenium WebDriver实例
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum


class BrowserStatus(Enum):
    """浏览器状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    CLOSED = "closed"


@dataclass
class BrowserInstance:
    """浏览器实例信息"""
    instance_id: str
    driver: webdriver.Chrome
    env_id: str
    status: BrowserStatus = BrowserStatus.IDLE
    created_time: float = None
    last_used: float = None
    current_url: str = ""
    error_count: int = 0
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()
        if self.last_used is None:
            self.last_used = time.time()


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, hubstudio_api=None, logger=None):
        self.hubstudio_api = hubstudio_api
        self.logger = logger
        
        # 浏览器实例管理
        self._instances = {}  # instance_id -> BrowserInstance
        self._env_instances = {}  # env_id -> instance_id
        self._lock = threading.RLock()
        self._instance_counter = 0
        
        # 配置
        self.default_timeout = 30
        self.max_instances = 10
        self.cleanup_interval = 300  # 5分钟清理一次
        
        # 启动清理定时器
        self._start_cleanup_timer()
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[浏览器管理器] {message}")
    
    def create_browser(self, env_id: str, debugger_address: str) -> Optional[BrowserInstance]:
        """创建浏览器实例"""
        with self._lock:
            try:
                # 检查是否已存在该环境的浏览器实例
                if env_id in self._env_instances:
                    existing_id = self._env_instances[env_id]
                    if existing_id in self._instances:
                        self._log('info', f'环境 {env_id} 的浏览器实例已存在')
                        return self._instances[existing_id]
                
                # 检查实例数量限制
                if len(self._instances) >= self.max_instances:
                    self._log('warning', f'浏览器实例数量已达上限 ({self.max_instances})')
                    return None
                
                self._log('info', f'正在为环境 {env_id} 创建浏览器实例...')
                
                # 创建Chrome选项
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", debugger_address)
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--disable-extensions")
                chrome_options.add_argument("--disable-plugins")
                chrome_options.add_argument("--disable-images")
                
                # 创建WebDriver实例
                driver = webdriver.Chrome(options=chrome_options)
                driver.set_page_load_timeout(self.default_timeout)
                driver.implicitly_wait(10)
                
                # 创建浏览器实例
                self._instance_counter += 1
                instance_id = f"browser_{self._instance_counter:04d}"
                
                instance = BrowserInstance(
                    instance_id=instance_id,
                    driver=driver,
                    env_id=env_id,
                    status=BrowserStatus.IDLE
                )
                
                # 保存实例
                self._instances[instance_id] = instance
                self._env_instances[env_id] = instance_id
                
                self._log('info', f'浏览器实例创建成功: {instance_id}')
                return instance
                
            except WebDriverException as e:
                self._log('error', f'创建浏览器实例失败: {e}')
                return None
            except Exception as e:
                self._log('error', f'创建浏览器实例异常: {e}')
                return None
    
    def get_browser(self, env_id: str) -> Optional[BrowserInstance]:
        """获取指定环境的浏览器实例"""
        with self._lock:
            if env_id in self._env_instances:
                instance_id = self._env_instances[env_id]
                return self._instances.get(instance_id)
            return None
    
    def get_idle_browser(self) -> Optional[BrowserInstance]:
        """获取空闲的浏览器实例"""
        with self._lock:
            for instance in self._instances.values():
                if instance.status == BrowserStatus.IDLE:
                    return instance
            return None
    
    def set_browser_busy(self, instance_id: str) -> bool:
        """设置浏览器为忙碌状态"""
        with self._lock:
            if instance_id in self._instances:
                instance = self._instances[instance_id]
                instance.status = BrowserStatus.BUSY
                instance.last_used = time.time()
                return True
            return False
    
    def set_browser_idle(self, instance_id: str) -> bool:
        """设置浏览器为空闲状态"""
        with self._lock:
            if instance_id in self._instances:
                instance = self._instances[instance_id]
                instance.status = BrowserStatus.IDLE
                instance.last_used = time.time()
                return True
            return False
    
    def close_browser(self, instance_id: str) -> bool:
        """关闭浏览器实例"""
        with self._lock:
            try:
                if instance_id not in self._instances:
                    return False
                
                instance = self._instances[instance_id]
                
                # 关闭WebDriver
                try:
                    instance.driver.quit()
                except Exception as e:
                    self._log('warning', f'关闭WebDriver时出错: {e}')
                
                # 更新状态
                instance.status = BrowserStatus.CLOSED
                
                # 从映射中移除
                if instance.env_id in self._env_instances:
                    del self._env_instances[instance.env_id]
                
                # 从实例列表中移除
                del self._instances[instance_id]
                
                self._log('info', f'浏览器实例已关闭: {instance_id}')
                return True
                
            except Exception as e:
                self._log('error', f'关闭浏览器实例失败: {e}')
                return False
    
    def close_all_browsers(self):
        """关闭所有浏览器实例"""
        with self._lock:
            instance_ids = list(self._instances.keys())
            for instance_id in instance_ids:
                self.close_browser(instance_id)
            
            self._log('info', '所有浏览器实例已关闭')
    
    def navigate_to(self, instance_id: str, url: str) -> bool:
        """导航到指定URL"""
        with self._lock:
            try:
                if instance_id not in self._instances:
                    return False
                
                instance = self._instances[instance_id]
                instance.driver.get(url)
                instance.current_url = url
                instance.last_used = time.time()
                
                self._log('info', f'浏览器 {instance_id} 导航到: {url}')
                return True
                
            except Exception as e:
                self._log('error', f'导航失败: {e}')
                self._handle_browser_error(instance_id, e)
                return False
    
    def wait_for_element(self, instance_id: str, by: By, value: str, 
                        timeout: int = None) -> bool:
        """等待元素出现"""
        with self._lock:
            try:
                if instance_id not in self._instances:
                    return False
                
                instance = self._instances[instance_id]
                wait_timeout = timeout or self.default_timeout
                
                wait = WebDriverWait(instance.driver, wait_timeout)
                wait.until(EC.presence_of_element_located((by, value)))
                
                instance.last_used = time.time()
                return True
                
            except TimeoutException:
                self._log('warning', f'等待元素超时: {value}')
                return False
            except Exception as e:
                self._log('error', f'等待元素失败: {e}')
                self._handle_browser_error(instance_id, e)
                return False
    
    def execute_script(self, instance_id: str, script: str) -> Any:
        """执行JavaScript脚本"""
        with self._lock:
            try:
                if instance_id not in self._instances:
                    return None
                
                instance = self._instances[instance_id]
                result = instance.driver.execute_script(script)
                instance.last_used = time.time()
                
                return result
                
            except Exception as e:
                self._log('error', f'执行脚本失败: {e}')
                self._handle_browser_error(instance_id, e)
                return None
    
    def take_screenshot(self, instance_id: str, filename: str = None) -> Optional[str]:
        """截取屏幕截图"""
        with self._lock:
            try:
                if instance_id not in self._instances:
                    return None
                
                instance = self._instances[instance_id]
                
                if filename:
                    instance.driver.save_screenshot(filename)
                    return filename
                else:
                    return instance.driver.get_screenshot_as_base64()
                
            except Exception as e:
                self._log('error', f'截图失败: {e}')
                return None
    
    def _handle_browser_error(self, instance_id: str, error: Exception):
        """处理浏览器错误"""
        if instance_id in self._instances:
            instance = self._instances[instance_id]
            instance.status = BrowserStatus.ERROR
            instance.error_count += 1
            
            # 如果错误次数过多，关闭浏览器
            if instance.error_count >= 3:
                self._log('warning', f'浏览器 {instance_id} 错误次数过多，将被关闭')
                self.close_browser(instance_id)
    
    def _start_cleanup_timer(self):
        """启动清理定时器"""
        def cleanup():
            try:
                self._cleanup_inactive_browsers()
            except Exception as e:
                self._log('error', f'清理浏览器实例失败: {e}')
            
            # 继续下一次清理
            timer = threading.Timer(self.cleanup_interval, cleanup)
            timer.daemon = True
            timer.start()
        
        # 启动第一次清理
        timer = threading.Timer(self.cleanup_interval, cleanup)
        timer.daemon = True
        timer.start()
    
    def _cleanup_inactive_browsers(self):
        """清理不活跃的浏览器实例"""
        with self._lock:
            current_time = time.time()
            inactive_threshold = 1800  # 30分钟不活跃
            
            inactive_instances = []
            for instance_id, instance in self._instances.items():
                if (current_time - instance.last_used > inactive_threshold and 
                    instance.status == BrowserStatus.IDLE):
                    inactive_instances.append(instance_id)
            
            for instance_id in inactive_instances:
                self._log('info', f'清理不活跃的浏览器实例: {instance_id}')
                self.close_browser(instance_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取浏览器管理统计信息"""
        with self._lock:
            status_count = {}
            for status in BrowserStatus:
                status_count[status.value] = 0
            
            total_errors = 0
            for instance in self._instances.values():
                status_count[instance.status.value] += 1
                total_errors += instance.error_count
            
            return {
                'total_instances': len(self._instances),
                'status_distribution': status_count,
                'total_errors': total_errors,
                'max_instances': self.max_instances,
                'active_environments': len(self._env_instances)
            }
    
    def get_instance_info(self, instance_id: str) -> Optional[Dict[str, Any]]:
        """获取浏览器实例信息"""
        with self._lock:
            if instance_id not in self._instances:
                return None
            
            instance = self._instances[instance_id]
            return {
                'instance_id': instance.instance_id,
                'env_id': instance.env_id,
                'status': instance.status.value,
                'created_time': instance.created_time,
                'last_used': instance.last_used,
                'current_url': instance.current_url,
                'error_count': instance.error_count
            }
    
    def list_instances(self) -> List[Dict[str, Any]]:
        """列出所有浏览器实例"""
        with self._lock:
            instances_info = []
            for instance_id in self._instances:
                info = self.get_instance_info(instance_id)
                if info:
                    instances_info.append(info)
            return instances_info
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        with self._lock:
            healthy_count = 0
            error_count = 0
            
            for instance in self._instances.values():
                try:
                    # 尝试获取当前URL来检查浏览器是否正常
                    current_url = instance.driver.current_url
                    instance.current_url = current_url
                    healthy_count += 1
                except Exception:
                    error_count += 1
                    instance.status = BrowserStatus.ERROR
            
            return {
                'total_instances': len(self._instances),
                'healthy_instances': healthy_count,
                'error_instances': error_count,
                'health_ratio': healthy_count / len(self._instances) if self._instances else 1.0
            }
    
    def restart_browser(self, instance_id: str) -> bool:
        """重启浏览器实例"""
        with self._lock:
            if instance_id not in self._instances:
                return False
            
            instance = self._instances[instance_id]
            env_id = instance.env_id
            
            # 关闭当前实例
            self.close_browser(instance_id)
            
            # 重新获取环境信息并创建新实例
            if self.hubstudio_api:
                env_info = self.hubstudio_api.get_environment_info(env_id)
                if env_info and env_info.status.value == 'running':
                    # 这里需要从环境信息中获取debugger地址
                    # 实际实现时需要根据HubStudio API的具体返回格式调整
                    debugger_address = "127.0.0.1:9222"  # 示例地址
                    new_instance = self.create_browser(env_id, debugger_address)
                    return new_instance is not None
            
            return False
    
    def __del__(self):
        """析构函数，确保所有浏览器实例被关闭"""
        try:
            self.close_all_browsers()
        except Exception:
            pass