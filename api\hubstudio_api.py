"""
HubStudio API管理器 - 封装HubStudio API调用
"""

import requests
import json
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum


class EnvironmentStatus(Enum):
    """环境状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class Environment:
    """环境信息"""
    env_id: str
    name: str
    status: EnvironmentStatus
    browser_type: str = "chrome"
    proxy_info: Dict = None
    created_time: str = ""
    last_used: str = ""
    
    def __post_init__(self):
        if self.proxy_info is None:
            self.proxy_info = {}


class HubStudioAPI:
    """HubStudio API管理器"""
    
    def __init__(self, config_manager=None, logger=None):
        self.config_manager = config_manager
        self.logger = logger
        
        # API配置
        self.api_url = self._get_config('BROWSER', 'api_url', 'http://127.0.0.1:50325')
        self.api_id = self._get_config('HUBSTUDIO', 'api_id', '')
        self.api_secret = self._get_config('HUBSTUDIO', 'api_secret', '')
        self.timeout = int(self._get_config('BROWSER', 'wait_timeout', '30'))
        self.retry_count = int(self._get_config('BROWSER', 'retry_count', '3'))
        
        # 状态管理
        self._connected = False
        self._last_check_time = 0
        self._check_interval = 30  # 30秒检查一次连接状态
        self._lock = threading.RLock()
        
        # 缓存
        self._environments_cache = []
        self._cache_expire_time = 0
        self._cache_duration = 60  # 缓存1分钟
        
        # 会话管理
        self.session = requests.Session()
        self.session.timeout = self.timeout
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'HubStudio-AutoUpload/1.0'
        })
    
    def _get_config(self, section: str, key: str, default: str = '') -> str:
        """获取配置值"""
        if self.config_manager:
            return str(self.config_manager.get(section, key, default))
        return default
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[HubStudio API] {message}")
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = self.session.get(f"{self.api_url}/api/v1/browser/active", timeout=10)
            self._connected = response.status_code == 200
            self._last_check_time = time.time()
            
            if self._connected:
                self._log('info', 'API连接测试成功')
            else:
                self._log('warning', f'API连接测试失败，状态码: {response.status_code}')
            
            return self._connected
            
        except requests.exceptions.RequestException as e:
            self._connected = False
            self._log('error', f'API连接测试失败: {e}')
            return False
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        # 如果距离上次检查超过间隔时间，重新检查
        if time.time() - self._last_check_time > self._check_interval:
            return self.test_connection()
        return self._connected
    
    def get_environments(self, force_refresh: bool = False) -> List[Environment]:
        """获取环境列表"""
        with self._lock:
            # 检查缓存
            if not force_refresh and self._is_cache_valid():
                self._log('debug', '使用缓存的环境列表')
                return self._environments_cache.copy()
            
            try:
                self._log('info', '正在获取环境列表...')
                
                # 构建请求参数
                params = {}
                if self.api_id:
                    params['api_id'] = self.api_id
                if self.api_secret:
                    params['api_secret'] = self.api_secret
                
                response = self.session.get(
                    f"{self.api_url}/api/v1/browser/list",
                    params=params
                )
                response.raise_for_status()
                
                data = response.json()
                environments = []
                
                if data.get('code') == 0 and 'data' in data:
                    for env_data in data['data']:
                        env = Environment(
                            env_id=str(env_data.get('id', '')),
                            name=env_data.get('name', ''),
                            status=self._parse_status(env_data.get('status', '')),
                            browser_type=env_data.get('browser_type', 'chrome'),
                            proxy_info=env_data.get('proxy', {}),
                            created_time=env_data.get('created_time', ''),
                            last_used=env_data.get('last_used', '')
                        )
                        environments.append(env)
                
                # 更新缓存
                self._environments_cache = environments
                self._cache_expire_time = time.time() + self._cache_duration
                
                self._log('info', f'成功获取 {len(environments)} 个环境')
                return environments.copy()
                
            except requests.exceptions.RequestException as e:
                self._log('error', f'获取环境列表失败: {e}')
                return []
            except Exception as e:
                self._log('error', f'解析环境列表失败: {e}')
                return []
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        return (self._environments_cache and 
                time.time() < self._cache_expire_time)
    
    def _parse_status(self, status_str: str) -> EnvironmentStatus:
        """解析环境状态"""
        status_map = {
            'stopped': EnvironmentStatus.STOPPED,
            'starting': EnvironmentStatus.STARTING,
            'running': EnvironmentStatus.RUNNING,
            'stopping': EnvironmentStatus.STOPPING,
            'error': EnvironmentStatus.ERROR
        }
        return status_map.get(status_str.lower(), EnvironmentStatus.STOPPED)
    
    def start_environment(self, env_id: str) -> Dict[str, Any]:
        """启动环境"""
        try:
            self._log('info', f'正在启动环境: {env_id}')
            
            payload = {
                'id': env_id
            }
            
            if self.api_id:
                payload['api_id'] = self.api_id
            if self.api_secret:
                payload['api_secret'] = self.api_secret
            
            response = self.session.post(
                f"{self.api_url}/api/v1/browser/start",
                json=payload
            )
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                self._log('info', f'环境启动成功: {env_id}')
                # 清除缓存以强制刷新
                self._clear_cache()
                return {
                    'success': True,
                    'data': data.get('data', {}),
                    'message': '环境启动成功'
                }
            else:
                error_msg = data.get('msg', '未知错误')
                self._log('error', f'环境启动失败: {error_msg}')
                return {
                    'success': False,
                    'error': error_msg,
                    'message': f'环境启动失败: {error_msg}'
                }
                
        except requests.exceptions.RequestException as e:
            self._log('error', f'启动环境请求失败: {e}')
            return {
                'success': False,
                'error': str(e),
                'message': f'启动环境请求失败: {e}'
            }
        except Exception as e:
            self._log('error', f'启动环境异常: {e}')
            return {
                'success': False,
                'error': str(e),
                'message': f'启动环境异常: {e}'
            }
    
    def stop_environment(self, env_id: str) -> Dict[str, Any]:
        """停止环境"""
        try:
            self._log('info', f'正在停止环境: {env_id}')
            
            payload = {
                'id': env_id
            }
            
            if self.api_id:
                payload['api_id'] = self.api_id
            if self.api_secret:
                payload['api_secret'] = self.api_secret
            
            response = self.session.post(
                f"{self.api_url}/api/v1/browser/stop",
                json=payload
            )
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                self._log('info', f'环境停止成功: {env_id}')
                # 清除缓存以强制刷新
                self._clear_cache()
                return {
                    'success': True,
                    'message': '环境停止成功'
                }
            else:
                error_msg = data.get('msg', '未知错误')
                self._log('error', f'环境停止失败: {error_msg}')
                return {
                    'success': False,
                    'error': error_msg,
                    'message': f'环境停止失败: {error_msg}'
                }
                
        except requests.exceptions.RequestException as e:
            self._log('error', f'停止环境请求失败: {e}')
            return {
                'success': False,
                'error': str(e),
                'message': f'停止环境请求失败: {e}'
            }
        except Exception as e:
            self._log('error', f'停止环境异常: {e}')
            return {
                'success': False,
                'error': str(e),
                'message': f'停止环境异常: {e}'
            }
    
    def get_environment_info(self, env_id: str) -> Optional[Environment]:
        """获取特定环境信息"""
        environments = self.get_environments()
        for env in environments:
            if env.env_id == env_id:
                return env
        return None
    
    def get_running_environments(self) -> List[Environment]:
        """获取正在运行的环境"""
        environments = self.get_environments()
        return [env for env in environments if env.status == EnvironmentStatus.RUNNING]
    
    def _clear_cache(self):
        """清除缓存"""
        self._environments_cache.clear()
        self._cache_expire_time = 0
    
    def refresh_cache(self):
        """刷新缓存"""
        self.get_environments(force_refresh=True)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取API统计信息"""
        environments = self.get_environments()
        
        status_count = {}
        for status in EnvironmentStatus:
            status_count[status.value] = 0
        
        for env in environments:
            status_count[env.status.value] += 1
        
        return {
            'connected': self._connected,
            'last_check_time': self._last_check_time,
            'total_environments': len(environments),
            'status_distribution': status_count,
            'cache_valid': self._is_cache_valid(),
            'api_url': self.api_url
        }
    
    def update_config(self, api_url: str = None, api_id: str = None, 
                     api_secret: str = None, timeout: int = None):
        """更新API配置"""
        if api_url:
            self.api_url = api_url
        if api_id:
            self.api_id = api_id
        if api_secret:
            self.api_secret = api_secret
        if timeout:
            self.timeout = timeout
            self.session.timeout = timeout
        
        # 清除缓存和连接状态
        self._clear_cache()
        self._connected = False
        self._last_check_time = 0
        
        self._log('info', 'API配置已更新')
    
    def close(self):
        """关闭API连接"""
        try:
            self.session.close()
            self._log('info', 'API连接已关闭')
        except Exception as e:
            self._log('error', f'关闭API连接失败: {e}')