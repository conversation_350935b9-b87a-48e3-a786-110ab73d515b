"""
主窗口组件 - 应用程序主界面容器
"""

import tkinter as tk
from tkinter import ttk
import threading
from typing import Dict, Any, Optional


class MainWindow:
    """主窗口组件"""
    
    def __init__(self, root: tk.Tk, event_manager, state_manager):
        self.root = root
        self.event_manager = event_manager
        self.state_manager = state_manager
        
        # 窗口配置
        self.window_config = {
            'title': 'HubStudio + YouTube 自动化上传工具 v2.0',
            'width': 1200,
            'height': 800,
            'min_width': 1000,
            'min_height': 600,
            'resizable': True
        }
        
        # 界面组件
        self.main_container = None
        self.status_bar = None
        self.menu_bar = None
        
        # 状态变量
        self.is_maximized = False
        self.last_geometry = None
        
        # 初始化界面
        self._setup_window()
        self._create_menu()
        self._create_main_container()
        self._create_status_bar()
        self._setup_events()
        self._setup_state_bindings()
        
        # 应用主题
        self._apply_theme()
    
    def _setup_window(self):
        """设置窗口属性"""
        # 设置窗口标题
        self.root.title(self.window_config['title'])
        
        # 设置窗口大小和位置
        width = self.window_config['width']
        height = self.window_config['height']
        
        # 计算居中位置
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置最小尺寸
        self.root.minsize(self.window_config['min_width'], self.window_config['min_height'])
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('assets/icon.ico')
            pass
        except:
            pass
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_menu(self):
        """创建菜单栏"""
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self._import_config)
        file_menu.add_command(label="导出配置", command=self._export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理日志", command=self._clear_logs)
        tools_menu.add_command(label="重置配置", command=self._reset_config)
        tools_menu.add_command(label="检查更新", command=self._check_updates)
        
        # 视图菜单
        view_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="全屏", command=self._toggle_fullscreen)
        view_menu.add_command(label="最小化", command=self._minimize_window)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_main_container(self):
        """创建主容器"""
        # 创建主框架
        self.main_container = ttk.Frame(self.root)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 0))
        
        # 配置网格权重
        self.main_container.grid_rowconfigure(0, weight=1)
        self.main_container.grid_columnconfigure(0, weight=1)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(5, 10))
        
        # 状态标签
        self.status_label = ttk.Label(self.status_bar, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # 连接状态指示器
        self.connection_status = ttk.Label(self.status_bar, text="未连接", relief=tk.SUNKEN, width=10)
        self.connection_status.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 时间标签
        self.time_label = ttk.Label(self.status_bar, text="", relief=tk.SUNKEN, width=20)
        self.time_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 启动时间更新
        self._update_time()
    
    def _setup_events(self):
        """设置事件绑定"""
        # 窗口事件
        self.root.bind('<Configure>', self._on_window_configure)
        self.root.bind('<F11>', self._toggle_fullscreen)
        self.root.bind('<Control-q>', lambda e: self._on_closing())
        self.root.bind('<Control-m>', lambda e: self._minimize_window())
        
        # 应用程序事件
        if self.event_manager:
            self.event_manager.subscribe('status_message_changed', self._update_status_message)
            self.event_manager.subscribe('connection_status_changed', self._update_connection_status)
            self.event_manager.subscribe('application_closing', self._handle_application_closing)
    
    def _setup_state_bindings(self):
        """设置状态绑定"""
        if self.state_manager:
            # 监听状态变化
            try:
                self.state_manager.subscribe('connection_status', self._on_connection_status_changed)
                self.state_manager.subscribe('application_status', self._on_application_status_changed)
            except AttributeError:
                # StateManager可能没有subscribe方法，跳过
                pass
    
    def _apply_theme(self):
        """应用主题"""
        try:
            # 设置ttk样式
            style = ttk.Style()
            
            # 使用现代主题
            available_themes = style.theme_names()
            if 'clam' in available_themes:
                style.theme_use('clam')
            elif 'alt' in available_themes:
                style.theme_use('alt')
            
            # 自定义样式
            style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
            style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))
            style.configure('Status.TLabel', font=('Arial', 9))
            
        except Exception as e:
            print(f"应用主题失败: {e}")
    
    def _update_time(self):
        """更新时间显示"""
        try:
            import datetime
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.config(text=current_time)
            
            # 每秒更新一次
            self.root.after(1000, self._update_time)
        except Exception:
            pass
    
    def _on_window_configure(self, event):
        """窗口配置变化事件"""
        if event.widget == self.root:
            # 记录窗口几何信息
            if not self.is_maximized:
                self.last_geometry = self.root.geometry()
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            # 发布应用程序关闭事件
            if self.event_manager:
                self.event_manager.publish('application_closing', {})
            
            # 保存窗口状态
            self._save_window_state()
            
            # 销毁窗口
            self.root.quit()
            self.root.destroy()
            
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            self.root.destroy()
    
    def _save_window_state(self):
        """保存窗口状态"""
        try:
            if self.state_manager:
                window_state = {
                    'geometry': self.root.geometry(),
                    'is_maximized': self.is_maximized
                }
                self.state_manager.set('window_state', window_state)
        except Exception as e:
            print(f"保存窗口状态失败: {e}")
    
    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            if self.state_manager:
                window_state = self.state_manager.get('window_state')
                if window_state:
                    if window_state.get('geometry'):
                        self.root.geometry(window_state['geometry'])
                    if window_state.get('is_maximized'):
                        self._maximize_window()
        except Exception as e:
            print(f"恢复窗口状态失败: {e}")
    
    # ==================== 菜单事件处理 ====================
    
    def _import_config(self):
        """导入配置"""
        if self.event_manager:
            self.event_manager.publish('import_config_requested', {})
    
    def _export_config(self):
        """导出配置"""
        if self.event_manager:
            self.event_manager.publish('export_config_requested', {})
    
    def _clear_logs(self):
        """清理日志"""
        if self.event_manager:
            self.event_manager.publish('clear_logs_requested', {})
    
    def _reset_config(self):
        """重置配置"""
        if self.event_manager:
            self.event_manager.publish('reset_config_requested', {})
    
    def _check_updates(self):
        """检查更新"""
        if self.event_manager:
            self.event_manager.publish('check_updates_requested', {})
    
    def _show_help(self):
        """显示帮助"""
        if self.event_manager:
            self.event_manager.publish('show_help_requested', {})
    
    def _show_about(self):
        """显示关于"""
        if self.event_manager:
            self.event_manager.publish('show_about_requested', {})
    
    # ==================== 窗口操作 ====================
    
    def _toggle_fullscreen(self, event=None):
        """切换全屏"""
        try:
            current_state = self.root.attributes('-fullscreen')
            self.root.attributes('-fullscreen', not current_state)
            self.is_maximized = not current_state
        except Exception as e:
            print(f"切换全屏失败: {e}")
    
    def _minimize_window(self):
        """最小化窗口"""
        try:
            self.root.iconify()
        except Exception as e:
            print(f"最小化窗口失败: {e}")
    
    def _maximize_window(self):
        """最大化窗口"""
        try:
            self.root.state('zoomed')
            self.is_maximized = True
        except Exception as e:
            print(f"最大化窗口失败: {e}")
    
    # ==================== 状态更新 ====================
    
    def _update_status_message(self, message: str):
        """更新状态消息"""
        try:
            self.status_label.config(text=message)
        except Exception as e:
            print(f"更新状态消息失败: {e}")
    
    def _update_connection_status(self, status: str):
        """更新连接状态"""
        try:
            status_colors = {
                'connected': 'green',
                'connecting': 'orange', 
                'disconnected': 'red',
                'error': 'red'
            }
            
            self.connection_status.config(text=status)
            
            # 设置颜色（如果支持的话）
            color = status_colors.get(status.lower(), 'black')
            try:
                self.connection_status.config(foreground=color)
            except:
                pass
                
        except Exception as e:
            print(f"更新连接状态失败: {e}")
    
    def _on_connection_status_changed(self, change):
        """连接状态变化回调"""
        try:
            if hasattr(change, 'new_value'):
                self._update_connection_status(change.new_value)
            else:
                self._update_connection_status(str(change))
        except Exception as e:
            print(f"处理连接状态变化失败: {e}")
    
    def _on_application_status_changed(self, change):
        """应用程序状态变化回调"""
        try:
            if hasattr(change, 'new_value'):
                self._update_status_message(change.new_value)
            else:
                self._update_status_message(str(change))
        except Exception as e:
            print(f"处理应用程序状态变化失败: {e}")
    
    def _handle_application_closing(self, data):
        """处理应用程序关闭事件"""
        # 可以在这里添加清理逻辑
        pass
    
    # ==================== 公共接口 ====================
    
    def get_main_container(self) -> ttk.Frame:
        """获取主容器"""
        return self.main_container
    
    def get_root(self) -> tk.Tk:
        """获取根窗口"""
        return self.root
    
    def set_title(self, title: str):
        """设置窗口标题"""
        self.root.title(title)
    
    def show_message(self, message: str, message_type: str = 'info'):
        """显示消息"""
        from tkinter import messagebox
        
        if message_type == 'info':
            messagebox.showinfo("信息", message)
        elif message_type == 'warning':
            messagebox.showwarning("警告", message)
        elif message_type == 'error':
            messagebox.showerror("错误", message)
        else:
            messagebox.showinfo("消息", message)
    
    def ask_confirmation(self, message: str, title: str = "确认") -> bool:
        """询问确认"""
        from tkinter import messagebox
        return messagebox.askyesno(title, message)
    
    def center_window(self):
        """居中窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def get_window_info(self) -> Dict[str, Any]:
        """获取窗口信息"""
        return {
            'title': self.root.title(),
            'geometry': self.root.geometry(),
            'width': self.root.winfo_width(),
            'height': self.root.winfo_height(),
            'x': self.root.winfo_x(),
            'y': self.root.winfo_y(),
            'is_maximized': self.is_maximized,
            'is_fullscreen': self.root.attributes('-fullscreen')
        }