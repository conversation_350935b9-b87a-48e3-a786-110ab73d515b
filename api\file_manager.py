"""
文件管理器 - 处理视频文件的选择、验证和管理
"""

import os
import mimetypes
import hashlib
import threading
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import time


class FileStatus(Enum):
    """文件状态枚举"""
    PENDING = "pending"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class VideoFile:
    """视频文件信息"""
    file_path: str
    file_name: str = ""
    file_size: int = 0
    file_type: str = ""
    duration: float = 0.0
    status: FileStatus = FileStatus.PENDING
    upload_progress: float = 0.0
    error_message: str = ""
    created_time: float = field(default_factory=time.time)
    upload_start_time: float = 0.0
    upload_end_time: float = 0.0
    file_hash: str = ""
    
    # 视频元信息
    title: str = ""
    description: str = ""
    tags: List[str] = field(default_factory=list)
    privacy: str = "private"
    category: str = ""
    thumbnail_path: str = ""
    
    def __post_init__(self):
        if not self.file_name:
            self.file_name = os.path.basename(self.file_path)
        if not self.file_size and os.path.exists(self.file_path):
            self.file_size = os.path.getsize(self.file_path)
        if not self.file_type:
            self.file_type = mimetypes.guess_type(self.file_path)[0] or ""


class FileManager:
    """文件管理器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self._files = {}  # file_path -> VideoFile
        self._lock = threading.RLock()
        
        # 支持的视频格式
        self.supported_formats = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', 
            '.webm', '.m4v', '.3gp', '.ogv', '.ts', '.mts'
        }
        
        # 文件大小限制 (字节)
        self.min_file_size = 1024 * 1024  # 1MB
        self.max_file_size = 128 * 1024 * 1024 * 1024  # 128GB
        
        # 观察者列表
        self._observers = []
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[文件管理器] {message}")
    
    def add_observer(self, observer: Callable[[str, VideoFile], None]):
        """添加文件状态变更观察者"""
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer: Callable[[str, VideoFile], None]):
        """移除文件状态变更观察者"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self, event_type: str, video_file: VideoFile):
        """通知观察者"""
        for observer in self._observers:
            try:
                observer(event_type, video_file)
            except Exception as e:
                self._log('error', f'通知观察者失败: {e}')
    
    def add_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """添加文件到管理器"""
        with self._lock:
            results = {
                'added': [],
                'skipped': [],
                'errors': []
            }
            
            for file_path in file_paths:
                try:
                    # 验证文件
                    validation_result = self.validate_file(file_path)
                    if not validation_result['valid']:
                        results['errors'].append({
                            'file_path': file_path,
                            'error': validation_result['error']
                        })
                        continue
                    
                    # 检查是否已存在
                    if file_path in self._files:
                        results['skipped'].append(file_path)
                        continue
                    
                    # 创建视频文件对象
                    video_file = VideoFile(file_path=file_path)
                    
                    # 计算文件哈希
                    video_file.file_hash = self._calculate_file_hash(file_path)
                    
                    # 获取视频时长（如果可能）
                    video_file.duration = self._get_video_duration(file_path)
                    
                    # 添加到管理器
                    self._files[file_path] = video_file
                    results['added'].append(file_path)
                    
                    # 通知观察者
                    self._notify_observers('file_added', video_file)
                    
                    self._log('info', f'文件已添加: {video_file.file_name}')
                    
                except Exception as e:
                    results['errors'].append({
                        'file_path': file_path,
                        'error': str(e)
                    })
                    self._log('error', f'添加文件失败 {file_path}: {e}')
            
            return results
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """验证文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {'valid': False, 'error': '文件不存在'}
            
            # 检查是否为文件
            if not os.path.isfile(file_path):
                return {'valid': False, 'error': '不是有效的文件'}
            
            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return {
                    'valid': False, 
                    'error': f'不支持的文件格式: {file_ext}'
                }
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size < self.min_file_size:
                return {
                    'valid': False, 
                    'error': f'文件太小 (最小: {self.min_file_size // 1024 // 1024}MB)'
                }
            
            if file_size > self.max_file_size:
                return {
                    'valid': False, 
                    'error': f'文件太大 (最大: {self.max_file_size // 1024 // 1024 // 1024}GB)'
                }
            
            # 检查文件是否可读
            try:
                with open(file_path, 'rb') as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception as e:
                return {'valid': False, 'error': f'文件无法读取: {e}'}
            
            return {'valid': True, 'error': None}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证文件时出错: {e}'}
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # 只读取文件的前1MB来计算哈希，提高性能
                chunk = f.read(1024 * 1024)
                hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self._log('warning', f'计算文件哈希失败: {e}')
            return ""
    
    def _get_video_duration(self, file_path: str) -> float:
        """获取视频时长（秒）"""
        try:
            # 这里可以使用ffmpeg-python或其他库来获取视频信息
            # 为了简化，这里返回0，实际项目中可以集成相应的库
            return 0.0
        except Exception as e:
            self._log('warning', f'获取视频时长失败: {e}')
            return 0.0
    
    def remove_file(self, file_path: str) -> bool:
        """从管理器中移除文件"""
        with self._lock:
            if file_path in self._files:
                video_file = self._files[file_path]
                del self._files[file_path]
                
                # 通知观察者
                self._notify_observers('file_removed', video_file)
                
                self._log('info', f'文件已移除: {video_file.file_name}')
                return True
            return False
    
    def clear_files(self):
        """清空所有文件"""
        with self._lock:
            file_paths = list(self._files.keys())
            for file_path in file_paths:
                self.remove_file(file_path)
            
            self._log('info', '所有文件已清空')
    
    def get_file(self, file_path: str) -> Optional[VideoFile]:
        """获取文件信息"""
        with self._lock:
            return self._files.get(file_path)
    
    def get_all_files(self) -> List[VideoFile]:
        """获取所有文件"""
        with self._lock:
            return list(self._files.values())
    
    def get_files_by_status(self, status: FileStatus) -> List[VideoFile]:
        """根据状态获取文件"""
        with self._lock:
            return [f for f in self._files.values() if f.status == status]
    
    def update_file_status(self, file_path: str, status: FileStatus, 
                          error_message: str = "", progress: float = 0.0) -> bool:
        """更新文件状态"""
        with self._lock:
            if file_path not in self._files:
                return False
            
            video_file = self._files[file_path]
            old_status = video_file.status
            
            video_file.status = status
            video_file.upload_progress = progress
            video_file.error_message = error_message
            
            # 更新时间戳
            if status == FileStatus.UPLOADING and old_status != FileStatus.UPLOADING:
                video_file.upload_start_time = time.time()
            elif status in [FileStatus.COMPLETED, FileStatus.FAILED, FileStatus.CANCELLED]:
                video_file.upload_end_time = time.time()
            
            # 通知观察者
            self._notify_observers('status_changed', video_file)
            
            return True
    
    def update_file_progress(self, file_path: str, progress: float) -> bool:
        """更新文件上传进度"""
        with self._lock:
            if file_path not in self._files:
                return False
            
            video_file = self._files[file_path]
            video_file.upload_progress = max(0.0, min(100.0, progress))
            
            # 通知观察者
            self._notify_observers('progress_updated', video_file)
            
            return True
    
    def update_file_metadata(self, file_path: str, title: str = None, 
                           description: str = None, tags: List[str] = None,
                           privacy: str = None, category: str = None,
                           thumbnail_path: str = None) -> bool:
        """更新文件元数据"""
        with self._lock:
            if file_path not in self._files:
                return False
            
            video_file = self._files[file_path]
            
            if title is not None:
                video_file.title = title
            if description is not None:
                video_file.description = description
            if tags is not None:
                video_file.tags = tags.copy()
            if privacy is not None:
                video_file.privacy = privacy
            if category is not None:
                video_file.category = category
            if thumbnail_path is not None:
                video_file.thumbnail_path = thumbnail_path
            
            # 通知观察者
            self._notify_observers('metadata_updated', video_file)
            
            return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取文件统计信息"""
        with self._lock:
            total_files = len(self._files)
            total_size = sum(f.file_size for f in self._files.values())
            
            status_count = {}
            for status in FileStatus:
                status_count[status.value] = len([f for f in self._files.values() if f.status == status])
            
            # 计算平均上传时间
            completed_files = [f for f in self._files.values() 
                             if f.status == FileStatus.COMPLETED and f.upload_end_time > 0]
            avg_upload_time = 0.0
            if completed_files:
                total_upload_time = sum(f.upload_end_time - f.upload_start_time for f in completed_files)
                avg_upload_time = total_upload_time / len(completed_files)
            
            return {
                'total_files': total_files,
                'total_size': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2),
                'status_distribution': status_count,
                'average_upload_time': round(avg_upload_time, 2),
                'supported_formats': list(self.supported_formats)
            }
    
    def get_pending_files(self) -> List[VideoFile]:
        """获取待上传的文件"""
        return self.get_files_by_status(FileStatus.PENDING)
    
    def get_uploading_files(self) -> List[VideoFile]:
        """获取正在上传的文件"""
        return self.get_files_by_status(FileStatus.UPLOADING)
    
    def get_completed_files(self) -> List[VideoFile]:
        """获取已完成的文件"""
        return self.get_files_by_status(FileStatus.COMPLETED)
    
    def get_failed_files(self) -> List[VideoFile]:
        """获取失败的文件"""
        return self.get_files_by_status(FileStatus.FAILED)
    
    def reset_file_status(self, file_path: str) -> bool:
        """重置文件状态为待上传"""
        return self.update_file_status(file_path, FileStatus.PENDING, "", 0.0)
    
    def reset_failed_files(self) -> int:
        """重置所有失败文件的状态"""
        with self._lock:
            failed_files = self.get_failed_files()
            count = 0
            for video_file in failed_files:
                if self.reset_file_status(video_file.file_path):
                    count += 1
            
            self._log('info', f'已重置 {count} 个失败文件的状态')
            return count
    
    def export_file_list(self, format: str = "json") -> str:
        """导出文件列表"""
        with self._lock:
            if format == "json":
                import json
                data = []
                for video_file in self._files.values():
                    data.append({
                        'file_path': video_file.file_path,
                        'file_name': video_file.file_name,
                        'file_size': video_file.file_size,
                        'file_type': video_file.file_type,
                        'status': video_file.status.value,
                        'upload_progress': video_file.upload_progress,
                        'title': video_file.title,
                        'description': video_file.description,
                        'tags': video_file.tags,
                        'privacy': video_file.privacy
                    })
                return json.dumps(data, ensure_ascii=False, indent=2)
            else:
                # CSV格式
                lines = ['文件路径,文件名,大小(MB),状态,进度(%),标题']
                for video_file in self._files.values():
                    size_mb = round(video_file.file_size / 1024 / 1024, 2)
                    lines.append(f'"{video_file.file_path}","{video_file.file_name}",'
                               f'{size_mb},{video_file.status.value},'
                               f'{video_file.upload_progress},"{video_file.title}"')
                return '\n'.join(lines)
    
    def import_file_metadata(self, metadata_file: str) -> bool:
        """从文件导入元数据"""
        try:
            import json
            with open(metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            updated_count = 0
            for item in data:
                file_path = item.get('file_path')
                if file_path and file_path in self._files:
                    self.update_file_metadata(
                        file_path=file_path,
                        title=item.get('title'),
                        description=item.get('description'),
                        tags=item.get('tags'),
                        privacy=item.get('privacy'),
                        category=item.get('category')
                    )
                    updated_count += 1
            
            self._log('info', f'已导入 {updated_count} 个文件的元数据')
            return True
            
        except Exception as e:
            self._log('error', f'导入元数据失败: {e}')
            return False
    
    def cleanup_completed_files(self, keep_recent_hours: int = 24) -> int:
        """清理已完成的文件记录"""
        with self._lock:
            cutoff_time = time.time() - (keep_recent_hours * 3600)
            completed_files = [f for f in self._files.values() 
                             if f.status == FileStatus.COMPLETED and f.upload_end_time < cutoff_time]
            
            removed_count = 0
            for video_file in completed_files:
                if self.remove_file(video_file.file_path):
                    removed_count += 1
            
            self._log('info', f'已清理 {removed_count} 个已完成的文件记录')
            return removed_count