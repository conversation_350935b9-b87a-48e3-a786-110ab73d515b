"""
主窗口 - 现代化的GUI主界面
"""

import tkinter as tk
import customtkinter as ctk
from typing import Dict, Any, Optional
import threading
import time

from .theme_manager import theme_manager
from .components.file_selector import FileSelectorComponent
from .dialogs.api_settings_dialog import APISettingsDialog
from ..core.base_module import ModuleManager, Event, EventType
from ..core.config_manager import ConfigManager
from ..core.logger import get_logger


class StatusBar(ctk.CTkFrame):
    """状态栏组件 - 优化样式"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # 应用框架样式
        frame_style = theme_manager.create_frame_style('card')
        self.configure(**frame_style, height=50)

        # 配置网格
        self.grid_columnconfigure(1, weight=1)

        # 状态指示器
        self.status_indicator = ctk.CTkLabel(
            self,
            text="●",
            width=25,
            **theme_manager.create_label_style('success')
        )
        self.status_indicator.grid(row=0, column=0, padx=(15, 8), pady=12)

        # 状态文本
        self.status_label = ctk.CTkLabel(
            self,
            text="🚀 系统就绪",
            anchor="w",
            **theme_manager.create_label_style('default')
        )
        self.status_label.grid(row=0, column=1, padx=8, pady=12, sticky="ew")

        # 连接状态
        self.connection_label = ctk.CTkLabel(
            self,
            text="🔌 API: 未连接",
            **theme_manager.create_label_style('small')
        )
        self.connection_label.grid(row=0, column=2, padx=8, pady=12)

        # 时间标签
        self.time_label = ctk.CTkLabel(
            self,
            text="",
            **theme_manager.create_label_style('small')
        )
        self.time_label.grid(row=0, column=3, padx=(8, 15), pady=12)

        # 启动时间更新
        self._update_time()
    
    def _update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%H:%M:%S")
        self.time_label.configure(text=current_time)
        self.after(1000, self._update_time)
    
    def set_status(self, message: str, status_type: str = "info"):
        """设置状态信息"""
        self.status_label.configure(text=message)
        
        # 更新状态指示器颜色
        color = theme_manager.get_status_color(status_type)
        self.status_indicator.configure(text_color=color)
    
    def set_connection_status(self, connected: bool):
        """设置连接状态"""
        if connected:
            self.connection_label.configure(
                text="🟢 API: 已连接",
                text_color=theme_manager.get_status_text_color('success')
            )
        else:
            self.connection_label.configure(
                text="🔴 API: 未连接",
                text_color=theme_manager.get_status_text_color('error')
            )


class SidePanel(ctk.CTkFrame):
    """侧边面板 - 优化布局"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # 应用框架样式
        frame_style = theme_manager.create_frame_style('panel')
        self.configure(**frame_style)

        # 配置网格 - 优化权重分配
        self.grid_rowconfigure(12, weight=1)  # 底部留白

        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI - 优化布局"""
        # 标题 - 更大更醒目
        title_label = ctk.CTkLabel(
            self,
            text="控制面板",
            **theme_manager.create_label_style('heading')
        )
        title_label.grid(row=0, column=0, padx=20, pady=(25, 20), sticky="w")

        # 分隔线
        separator1 = ctk.CTkFrame(self, height=2, fg_color=theme_manager.get_color('border'))
        separator1.grid(row=1, column=0, padx=20, pady=5, sticky="ew")

        # 浏览器环境部分
        env_label = ctk.CTkLabel(
            self,
            text="🌐 浏览器环境",
            **theme_manager.create_label_style('subheading')
        )
        env_label.grid(row=2, column=0, padx=20, pady=(15, 8), sticky="w")

        # 环境状态
        self.env_status_label = ctk.CTkLabel(
            self,
            text="检测中...",
            **theme_manager.create_label_style('small')
        )
        self.env_status_label.grid(row=3, column=0, padx=20, pady=(0, 8), sticky="w")

        # 刷新环境按钮
        self.refresh_env_btn = ctk.CTkButton(
            self,
            text="🔄 刷新环境",
            command=self._on_refresh_env_clicked,
            **theme_manager.create_button_style('secondary')
        )
        self.refresh_env_btn.grid(row=4, column=0, padx=20, pady=(0, 15), sticky="ew")
        
        # 分隔线
        separator2 = ctk.CTkFrame(self, height=2, fg_color=theme_manager.get_color('border'))
        separator2.grid(row=5, column=0, padx=20, pady=5, sticky="ew")

        # 上传控制部分
        upload_label = ctk.CTkLabel(
            self,
            text="⚡ 上传控制",
            **theme_manager.create_label_style('subheading')
        )
        upload_label.grid(row=6, column=0, padx=20, pady=(15, 8), sticky="w")

        # 并发数控制框架
        concurrent_frame = ctk.CTkFrame(self, fg_color="transparent")
        concurrent_frame.grid(row=7, column=0, padx=20, pady=(0, 10), sticky="ew")
        concurrent_frame.grid_columnconfigure(1, weight=1)

        concurrent_label = ctk.CTkLabel(
            concurrent_frame,
            text="并发数量:",
            **theme_manager.create_label_style('default')
        )
        concurrent_label.grid(row=0, column=0, sticky="w")

        self.concurrent_value_label = ctk.CTkLabel(
            concurrent_frame,
            text="3",
            **theme_manager.create_label_style('default')
        )
        self.concurrent_value_label.grid(row=0, column=2, sticky="e")

        self.concurrent_slider = ctk.CTkSlider(
            concurrent_frame,
            from_=1,
            to=10,
            number_of_steps=9,
            command=self._on_concurrent_changed,
            progress_color=theme_manager.get_color('primary'),
            button_color=theme_manager.get_color('primary'),
            button_hover_color=theme_manager.get_color('primary_hover')
        )
        self.concurrent_slider.set(3)
        self.concurrent_slider.grid(row=1, column=0, columnspan=3, pady=(5, 0), sticky="ew")
        
        # 上传按钮组
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.grid(row=8, column=0, padx=20, pady=(15, 10), sticky="ew")
        button_frame.grid_columnconfigure((0, 1), weight=1)

        self.upload_btn = ctk.CTkButton(
            button_frame,
            text="▶️ 开始上传",
            state="disabled",
            height=40,
            **theme_manager.create_button_style('success')
        )
        self.upload_btn.grid(row=0, column=0, padx=(0, 5), sticky="ew")

        self.stop_btn = ctk.CTkButton(
            button_frame,
            text="⏹️ 停止上传",
            state="disabled",
            height=40,
            **theme_manager.create_button_style('error')
        )
        self.stop_btn.grid(row=0, column=1, padx=(5, 0), sticky="ew")

        # 分隔线
        separator3 = ctk.CTkFrame(self, height=2, fg_color=theme_manager.get_color('border'))
        separator3.grid(row=9, column=0, padx=20, pady=15, sticky="ew")

        # 设置部分
        settings_label = ctk.CTkLabel(
            self,
            text="⚙️ 设置",
            **theme_manager.create_label_style('subheading')
        )
        settings_label.grid(row=10, column=0, padx=20, pady=(15, 10), sticky="w")

        # API设置按钮
        self.api_settings_btn = ctk.CTkButton(
            self,
            text="🔧 API设置",
            command=self._on_api_settings_clicked,
            **theme_manager.create_button_style('accent')
        )
        self.api_settings_btn.grid(row=11, column=0, padx=20, pady=(0, 25), sticky="ew")
    
    def _on_concurrent_changed(self, value):
        """并发数量变化回调"""
        concurrent_count = int(value)
        self.concurrent_value_label.configure(text=f"{concurrent_count}")

        # 这里可以通知上传控制模块更新并发数

    def _on_refresh_env_clicked(self):
        """刷新环境按钮点击回调"""
        self.refresh_env_btn.configure(text="🔄 刷新中...", state="disabled")

        def refresh_thread():
            try:
                self._refresh_browser_environments()
                # 在主线程中更新UI
                self.after(1000, lambda: self.refresh_env_btn.configure(text="🔄 刷新环境", state="normal"))
            except Exception as e:
                self.logger.error(f"刷新环境异常: {e}")
                self.after(1000, lambda: self.refresh_env_btn.configure(text="🔄 刷新环境", state="normal"))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def _on_api_settings_clicked(self):
        """API设置按钮点击回调"""
        # 获取当前API配置
        current_config = self._get_current_api_config()

        # 打开API设置对话框
        dialog = APISettingsDialog(
            parent=self,
            current_config=current_config,
            on_save_callback=self._save_api_config,
            test_connection_callback=self._test_api_connection
        )

    def _get_current_api_config(self) -> dict:
        """获取当前API配置"""
        try:
            # 从配置管理器获取当前配置
            return {
                'server_url': self.config_manager.get('hubstudio_api.base_url', 'http://127.0.0.1:50325'),
                'api_id': self.config_manager.get('hubstudio_api.api_id', ''),
                'api_secret': self.config_manager.get('hubstudio_api.api_secret', '')
            }
        except Exception as e:
            self.logger.error(f"获取API配置失败: {e}")
            return {
                'server_url': 'http://127.0.0.1:50325',
                'api_id': '',
                'api_secret': ''
            }

    def _save_api_config(self, config: dict) -> bool:
        """保存API配置"""
        try:
            # 保存到配置管理器
            self.config_manager.set('hubstudio_api.base_url', config['server_url'])
            self.config_manager.set('hubstudio_api.api_id', config['api_id'])
            self.config_manager.set('hubstudio_api.api_secret', config['api_secret'])
            self.config_manager.save_config()

            # 通知HubStudio API模块更新配置
            hubstudio_api = self.module_manager.get_module('HubStudioAPI')
            if hubstudio_api:
                success = hubstudio_api.set_credentials(
                    config['api_id'],
                    config['api_secret'],
                    config['server_url']
                )

                if success:
                    self.logger.info("API配置保存并应用成功")
                    # 更新状态栏连接状态
                    self.status_bar.set_connection_status(True)
                    # 刷新浏览器环境
                    self._refresh_browser_environments()
                else:
                    self.logger.warning("API配置保存成功但连接失败")
                    self.status_bar.set_connection_status(False)

                return True
            else:
                self.logger.warning("未找到HubStudio API模块")
                return False

        except Exception as e:
            self.logger.error(f"保存API配置失败: {e}")
            return False

    def _test_api_connection(self, server_url: str, api_id: str, api_secret: str) -> dict:
        """测试API连接"""
        try:
            import requests
            import time

            # 模拟测试延迟
            time.sleep(0.5)

            # 尝试连接基础API
            try:
                response = requests.get(f"{server_url}/api/v1/browser/active", timeout=10)
            except requests.exceptions.ConnectionError:
                return {
                    'success': False,
                    'message': '无法连接到服务器，请检查HubStudio是否已启动'
                }
            except requests.exceptions.Timeout:
                return {
                    'success': False,
                    'message': '连接超时，请检查网络连接'
                }

            if response.status_code == 200:
                # 尝试获取环境列表
                try:
                    env_response = requests.get(f"{server_url}/api/v1/browser/list", timeout=10)
                    env_count = 0

                    if env_response.status_code == 200:
                        try:
                            data = env_response.json()
                            if isinstance(data, dict) and 'data' in data:
                                env_count = len(data['data'])
                            elif isinstance(data, list):
                                env_count = len(data)
                        except:
                            pass

                    return {
                        'success': True,
                        'message': '连接成功',
                        'environment_count': env_count
                    }
                except Exception as e:
                    return {
                        'success': True,
                        'message': '基础连接成功，但获取环境列表失败',
                        'environment_count': 0
                    }
            else:
                return {
                    'success': False,
                    'message': f'服务器响应错误: {response.status_code}'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'连接异常: {str(e)}'
            }

    def _refresh_browser_environments(self):
        """刷新浏览器环境"""
        try:
            browser_env = self.module_manager.get_module('BrowserEnvironment')
            if browser_env:
                browser_env.refresh_environments()

                # 更新侧边面板的环境状态
                status = browser_env.get_status()
                stats = status.get('stats', {})
                online_count = stats.get('online_environments', 0)
                total_count = stats.get('total_environments', 0)

                self.side_panel.update_env_status(online_count, total_count)

        except Exception as e:
            self.logger.error(f"刷新浏览器环境失败: {e}")
    
    def update_env_status(self, online_count: int, total_count: int):
        """更新环境状态"""
        status_text = f"在线: {online_count}/{total_count}"
        self.env_status_label.configure(text=status_text)
    
    def set_upload_enabled(self, enabled: bool):
        """设置上传按钮状态"""
        if enabled:
            self.upload_btn.configure(state="normal")
        else:
            self.upload_btn.configure(state="disabled")
    
    def set_stop_enabled(self, enabled: bool):
        """设置停止按钮状态"""
        if enabled:
            self.stop_btn.configure(state="normal")
        else:
            self.stop_btn.configure(state="disabled")


class MainWindow:
    """主窗口类"""
    
    def __init__(self, app, config_manager: ConfigManager, module_manager: ModuleManager):
        self.app = app
        self.config_manager = config_manager
        self.module_manager = module_manager
        self.logger = get_logger("MainWindow")
        
        # 窗口配置
        self.window_width = self.config_manager.get('app.window_width', 1200)
        self.window_height = self.config_manager.get('app.window_height', 800)
        
        # 创建主窗口
        self.root = ctk.CTk()
        self._setup_window()
        self._setup_ui()
        self._setup_event_handlers()
        
        # 启动状态更新
        self._start_status_updates()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.root.title(f"{self.app.app_name} v{self.app.version}")
        self.root.geometry(f"{self.window_width}x{self.window_height}")
        
        # 设置最小尺寸
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("assets/icon.ico")
            pass
        except:
            pass
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 配置网格权重
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
    
    def _setup_ui(self):
        """设置用户界面 - 优化布局"""
        # 侧边面板 - 调整宽度
        self.side_panel = SidePanel(self.root, width=280)
        self.side_panel.grid(row=0, column=0, sticky="nsew", padx=(15, 8), pady=15)

        # 主内容区域 - 优化样式
        self.main_content = ctk.CTkFrame(
            self.root,
            **theme_manager.create_frame_style('card')
        )
        self.main_content.grid(row=0, column=1, sticky="nsew", padx=(8, 15), pady=15)
        self.main_content.grid_columnconfigure(0, weight=1)
        self.main_content.grid_rowconfigure(0, weight=1)

        # 文件选择器
        self.file_selector = FileSelectorComponent(
            self.main_content,
            on_files_changed=self._on_files_changed
        )
        self.file_selector.grid(row=0, column=0, sticky="nsew", padx=15, pady=15)

        # 状态栏 - 优化样式
        self.status_bar = StatusBar(self.root)
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky="ew", padx=15, pady=(0, 15))

        self.logger.info("GUI界面已创建")
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 绑定侧边面板按钮事件
        self.side_panel.refresh_env_btn.configure(command=self._refresh_environments)
        self.side_panel.upload_btn.configure(command=self._start_upload)
        self.side_panel.stop_btn.configure(command=self._stop_upload)
        self.side_panel.api_settings_btn.configure(command=self._show_api_settings)
        
        # 监听模块事件
        if self.module_manager:
            self.module_manager.add_global_event_listener(
                EventType.DATA_UPDATED,
                self._handle_module_event
            )
            
            self.module_manager.add_global_event_listener(
                EventType.SYSTEM_EVENT,
                self._handle_system_event
            )
    
    def _start_status_updates(self):
        """启动状态更新"""
        self._update_status()
    
    def _update_status(self):
        """更新状态信息"""
        try:
            # 更新浏览器环境状态
            browser_module = self.module_manager.get_module("BrowserEnvironment")
            if browser_module:
                environments = browser_module.get_environments()
                online_envs = browser_module.get_online_environments()
                self.side_panel.update_env_status(len(online_envs), len(environments))
            
            # 更新API连接状态
            api_module = self.module_manager.get_module("HubStudioAPI")
            if api_module:
                connected = api_module.is_connected()
                self.status_bar.set_connection_status(connected)
            
            # 更新上传按钮状态
            selected_files = self.file_selector.get_selected_files()
            self.side_panel.set_upload_enabled(len(selected_files) > 0)
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
        
        # 定时更新
        self.root.after(5000, self._update_status)
    
    def _on_files_changed(self, file_paths: list):
        """文件列表变化回调"""
        try:
            # 通知视频文件模块
            video_file_module = self.module_manager.get_module("VideoFile")
            if video_file_module:
                # 添加新文件
                current_files = set(video_file_module.get_files())
                new_files = [f for f in file_paths if f not in current_files]
                
                if new_files:
                    video_file_module.add_files(new_files)
            
            self.logger.info(f"文件列表已更新，共 {len(file_paths)} 个文件")
            
        except Exception as e:
            self.logger.error(f"处理文件变化失败: {e}")
    
    def _refresh_environments(self):
        """刷新浏览器环境"""
        try:
            browser_module = self.module_manager.get_module("BrowserEnvironment")
            if browser_module:
                success = browser_module.refresh_environments()
                if success:
                    self.status_bar.set_status("环境列表已刷新", "success")
                else:
                    self.status_bar.set_status("刷新环境列表失败", "error")
            
        except Exception as e:
            self.logger.error(f"刷新环境失败: {e}")
            self.status_bar.set_status("刷新环境失败", "error")
    
    def _start_upload(self):
        """开始上传"""
        try:
            # 获取选中的文件
            selected_files = self.file_selector.get_selected_files()
            if not selected_files:
                self.status_bar.set_status("请先选择要上传的文件", "warning")
                return
            
            # 获取视频配置
            video_config_module = self.module_manager.get_module("VideoConfig")
            upload_control_module = self.module_manager.get_module("UploadControl")
            
            if not video_config_module or not upload_control_module:
                self.status_bar.set_status("模块未就绪", "error")
                return
            
            # 准备上传配置
            file_configs = []
            for file_path in selected_files:
                config = video_config_module.get_video_config(file_path)
                if config:
                    file_configs.append({
                        'file_path': file_path,
                        'file_name': Path(file_path).name,
                        'file_size': Path(file_path).stat().st_size,
                        'video_config': config.to_dict()
                    })
            
            if not file_configs:
                self.status_bar.set_status("没有有效的视频配置", "warning")
                return
            
            # 创建上传会话
            session_id = upload_control_module.create_session(
                session_name=f"上传会话 {time.strftime('%Y%m%d_%H%M%S')}",
                file_configs=file_configs,
                max_concurrent=int(self.side_panel.concurrent_slider.get())
            )
            
            if session_id:
                # 开始上传
                success = upload_control_module.start_session(session_id)
                if success:
                    self.status_bar.set_status("上传已开始", "info")
                    self.side_panel.set_upload_enabled(False)
                    self.side_panel.set_stop_enabled(True)
                else:
                    self.status_bar.set_status("启动上传失败", "error")
            else:
                self.status_bar.set_status("创建上传会话失败", "error")
            
        except Exception as e:
            self.logger.error(f"开始上传失败: {e}")
            self.status_bar.set_status("开始上传失败", "error")
    
    def _stop_upload(self):
        """停止上传"""
        try:
            upload_control_module = self.module_manager.get_module("UploadControl")
            if upload_control_module:
                upload_control_module.stop_all_uploads()
                self.status_bar.set_status("上传已停止", "warning")
                self.side_panel.set_upload_enabled(True)
                self.side_panel.set_stop_enabled(False)
            
        except Exception as e:
            self.logger.error(f"停止上传失败: {e}")
            self.status_bar.set_status("停止上传失败", "error")
    
    def _show_api_settings(self):
        """显示API设置对话框"""
        # 这里可以创建一个API设置对话框
        self.status_bar.set_status("API设置功能开发中", "info")
    
    def _handle_module_event(self, event: Event):
        """处理模块事件"""
        try:
            if event.source == "VideoFile" and event.data:
                event_type = event.data.get('type')
                
                if event_type == 'file_updated':
                    # 更新文件状态显示
                    file_path = event.data.get('file_path')
                    file_data = event.data.get('file')
                    
                    if file_path and file_data:
                        status = file_data.get('status', 'unknown')
                        progress = file_data.get('upload_progress', 0)
                        self.file_selector.update_file_status(file_path, status, progress)
            
        except Exception as e:
            self.logger.error(f"处理模块事件失败: {e}")
    
    def _handle_system_event(self, event: Event):
        """处理系统事件"""
        try:
            if event.data:
                event_type = event.data.get('type')
                
                if event_type == 'api_connected':
                    self.status_bar.set_status("API连接成功", "success")
                elif event_type == 'api_connection_failed':
                    self.status_bar.set_status("API连接失败", "error")
            
        except Exception as e:
            self.logger.error(f"处理系统事件失败: {e}")
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            # 保存窗口配置
            self.config_manager.set('app.window_width', self.root.winfo_width())
            self.config_manager.set('app.window_height', self.root.winfo_height())
            
            # 关闭应用
            self.app.shutdown()
            
        except Exception as e:
            self.logger.error(f"关闭窗口失败: {e}")
        finally:
            self.root.destroy()
    
    def run(self):
        """运行GUI主循环"""
        try:
            self.logger.info("GUI主循环已启动")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI主循环异常: {e}")
            raise
    
    def destroy(self):
        """销毁窗口"""
        if self.root:
            self.root.destroy()
