2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:44:49 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:45:02 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028636DE4510>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INITIALIZING -> ModuleState.ERROR
2025-08-02 04:45:16 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在初始化视频文件模块...
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已启动
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块初始化成功
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在初始化视频配置模块...
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块初始化成功
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在初始化上传控制模块...
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 已启动 3 个上传工作线程
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块初始化成功
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在初始化HubStudio API模块...
2025-08-02 04:45:16 | [33mWARNING[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] API连接失败，模块以离线模式启动
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:45:16 | [33mWARNING[0m | HubStudio.App:_initialize_modules:172 - 以下模块初始化失败: ['BrowserEnvironment']
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_initialize_modules:178 - 模块状态摘要: {'total_modules': 5, 'active_modules': 4, 'error_modules': 1, 'inactive_modules': 0, 'modules': {'BrowserEnvironment': {'module_name': 'BrowserEnvironment', 'state': 'error', 'stats': {'total_environments': 0, 'online_environments': 0, 'busy_environments': 0, 'last_refresh_time': None, 'api_connection_status': False}, 'environments_count': 0, 'selected_count': 0, 'monitoring_active': False, 'api_base_url': 'http://127.0.0.1:50325'}, 'VideoFile': {'module_name': 'VideoFile', 'state': 'active', 'stats': {'total_files': 0, 'valid_files': 0, 'invalid_files': 0, 'total_size': 0, 'selected_count': 0, 'selected_size': 0}, 'supported_formats': ['.wmv', '.ogv', '.mkv', '.mp4', '.3gp', '.m4v', '.ts', '.mts', '.m2ts', '.flv', '.mov', '.webm', '.avi'], 'file_size_limits': {'min_size': 1048576, 'max_size': 137438953472}, 'validation_active': True, 'validation_queue_size': 0}, 'VideoConfig': {'module_name': 'VideoConfig', 'state': 'active', 'stats': {'total_templates': 1, 'configured_videos': 0, 'current_template': '默认模板'}, 'current_template': '默认模板', 'available_templates': ['默认模板'], 'privacy_levels': ['public', 'unlisted', 'private'], 'categories': ['1', '2', '10', '15', '17', '19', '20', '22', '23', '24', '25', '26', '27', '28', '29']}, 'UploadControl': {'module_name': 'UploadControl', 'state': 'active', 'stats': {'total_sessions': 0, 'active_sessions': 0, 'total_tasks': 0, 'completed_tasks': 0, 'failed_tasks': 0, 'active_uploads': 0, 'upload_speed': 0.0, 'eta': None}, 'max_concurrent': 3, 'current_session': None, 'active_tasks_count': 0, 'queue_size': 0, 'workers_active': True}, 'HubStudioAPI': {'module_name': 'HubStudioAPI', 'state': 'active', 'connected': False, 'credentials_configured': False, 'server_url': None, 'upload_stats': {'total_uploads': 0, 'successful_uploads': 0, 'failed_uploads': 0, 'total_upload_time': 0.0, 'average_upload_time': 0.0, 'last_upload_time': None}, 'monitoring_active': False, 'last_ping_time': 0}}}
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_setup_module_communication:211 - 模块间通信已设置
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:_initialize:85 - 应用程序初始化完成
2025-08-02 04:45:16 | [32mINFO[0m | HubStudio.App:run:325 - 正在启动GUI界面...
2025-08-02 04:45:17 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\.venv\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:shutdown:350 - 正在关闭应用程序...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在清理浏览器环境模块...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.ERROR -> ModuleState.INACTIVE
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 浏览器环境模块清理完成
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在清理视频文件模块...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已停止
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块清理完成
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在清理视频配置模块...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块清理完成
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在清理上传控制模块...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 所有上传已停止
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传工作线程已停止
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块清理完成
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在清理HubStudio API模块...
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] HubStudio API模块清理完成
2025-08-02 04:45:17 | [32mINFO[0m | HubStudio.App:shutdown:366 - 应用程序已关闭
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:51:30 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D11F020640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INITIALIZING -> ModuleState.ERROR
2025-08-02 04:51:45 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在初始化视频文件模块...
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已启动
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块初始化成功
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在初始化视频配置模块...
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块初始化成功
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在初始化上传控制模块...
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 已启动 3 个上传工作线程
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块初始化成功
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在初始化HubStudio API模块...
2025-08-02 04:51:45 | [33mWARNING[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] API连接失败，模块以离线模式启动
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:51:45 | [33mWARNING[0m | HubStudio.App:_initialize_modules:172 - 以下模块初始化失败: ['BrowserEnvironment']
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_initialize_modules:178 - 模块状态摘要: {'total_modules': 5, 'active_modules': 4, 'error_modules': 1, 'inactive_modules': 0, 'modules': {'BrowserEnvironment': {'module_name': 'BrowserEnvironment', 'state': 'error', 'stats': {'total_environments': 0, 'online_environments': 0, 'busy_environments': 0, 'last_refresh_time': None, 'api_connection_status': False}, 'environments_count': 0, 'selected_count': 0, 'monitoring_active': False, 'api_base_url': 'http://127.0.0.1:50325'}, 'VideoFile': {'module_name': 'VideoFile', 'state': 'active', 'stats': {'total_files': 0, 'valid_files': 0, 'invalid_files': 0, 'total_size': 0, 'selected_count': 0, 'selected_size': 0}, 'supported_formats': ['.ogv', '.flv', '.mts', '.3gp', '.mov', '.webm', '.wmv', '.avi', '.mkv', '.ts', '.mp4', '.m2ts', '.m4v'], 'file_size_limits': {'min_size': 1048576, 'max_size': 137438953472}, 'validation_active': True, 'validation_queue_size': 0}, 'VideoConfig': {'module_name': 'VideoConfig', 'state': 'active', 'stats': {'total_templates': 1, 'configured_videos': 0, 'current_template': '默认模板'}, 'current_template': '默认模板', 'available_templates': ['默认模板'], 'privacy_levels': ['public', 'unlisted', 'private'], 'categories': ['1', '2', '10', '15', '17', '19', '20', '22', '23', '24', '25', '26', '27', '28', '29']}, 'UploadControl': {'module_name': 'UploadControl', 'state': 'active', 'stats': {'total_sessions': 0, 'active_sessions': 0, 'total_tasks': 0, 'completed_tasks': 0, 'failed_tasks': 0, 'active_uploads': 0, 'upload_speed': 0.0, 'eta': None}, 'max_concurrent': 3, 'current_session': None, 'active_tasks_count': 0, 'queue_size': 0, 'workers_active': True}, 'HubStudioAPI': {'module_name': 'HubStudioAPI', 'state': 'active', 'connected': False, 'credentials_configured': False, 'server_url': None, 'upload_stats': {'total_uploads': 0, 'successful_uploads': 0, 'failed_uploads': 0, 'total_upload_time': 0.0, 'average_upload_time': 0.0, 'last_upload_time': None}, 'monitoring_active': False, 'last_ping_time': 0}}}
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_setup_module_communication:211 - 模块间通信已设置
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:_initialize:85 - 应用程序初始化完成
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.App:run:325 - 正在启动GUI界面...
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.MainWindow:_setup_ui:324 - GUI界面已创建
2025-08-02 04:51:45 | [32mINFO[0m | HubStudio.MainWindow:run:535 - GUI主循环已启动
2025-08-02 04:51:51 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在刷新环境列表...
2025-08-02 04:52:05 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 刷新环境列表异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/list (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D11EF8A8B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:53:54 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016CBA3A0640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INITIALIZING -> ModuleState.ERROR
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在初始化视频文件模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已启动
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块初始化成功
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在初始化视频配置模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块初始化成功
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在初始化上传控制模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 已启动 3 个上传工作线程
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块初始化成功
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在初始化HubStudio API模块...
2025-08-02 04:54:09 | [33mWARNING[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] API连接失败，模块以离线模式启动
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:54:09 | [33mWARNING[0m | HubStudio.App:_initialize_modules:172 - 以下模块初始化失败: ['BrowserEnvironment']
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_initialize_modules:178 - 模块状态摘要: {'total_modules': 5, 'active_modules': 4, 'error_modules': 1, 'inactive_modules': 0, 'modules': {'BrowserEnvironment': {'module_name': 'BrowserEnvironment', 'state': 'error', 'stats': {'total_environments': 0, 'online_environments': 0, 'busy_environments': 0, 'last_refresh_time': None, 'api_connection_status': False}, 'environments_count': 0, 'selected_count': 0, 'monitoring_active': False, 'api_base_url': 'http://127.0.0.1:50325'}, 'VideoFile': {'module_name': 'VideoFile', 'state': 'active', 'stats': {'total_files': 0, 'valid_files': 0, 'invalid_files': 0, 'total_size': 0, 'selected_count': 0, 'selected_size': 0}, 'supported_formats': ['.ts', '.ogv', '.m2ts', '.3gp', '.mkv', '.avi', '.mp4', '.mov', '.flv', '.mts', '.webm', '.m4v', '.wmv'], 'file_size_limits': {'min_size': 1048576, 'max_size': 137438953472}, 'validation_active': True, 'validation_queue_size': 0}, 'VideoConfig': {'module_name': 'VideoConfig', 'state': 'active', 'stats': {'total_templates': 1, 'configured_videos': 0, 'current_template': '默认模板'}, 'current_template': '默认模板', 'available_templates': ['默认模板'], 'privacy_levels': ['public', 'unlisted', 'private'], 'categories': ['1', '2', '10', '15', '17', '19', '20', '22', '23', '24', '25', '26', '27', '28', '29']}, 'UploadControl': {'module_name': 'UploadControl', 'state': 'active', 'stats': {'total_sessions': 0, 'active_sessions': 0, 'total_tasks': 0, 'completed_tasks': 0, 'failed_tasks': 0, 'active_uploads': 0, 'upload_speed': 0.0, 'eta': None}, 'max_concurrent': 3, 'current_session': None, 'active_tasks_count': 0, 'queue_size': 0, 'workers_active': True}, 'HubStudioAPI': {'module_name': 'HubStudioAPI', 'state': 'active', 'connected': False, 'credentials_configured': False, 'server_url': None, 'upload_stats': {'total_uploads': 0, 'successful_uploads': 0, 'failed_uploads': 0, 'total_upload_time': 0.0, 'average_upload_time': 0.0, 'last_upload_time': None}, 'monitoring_active': False, 'last_ping_time': 0}}}
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_setup_module_communication:211 - 模块间通信已设置
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_initialize:85 - 应用程序初始化完成
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:run:325 - 正在启动GUI界面...
2025-08-02 04:54:09 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:shutdown:350 - 正在关闭应用程序...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在清理浏览器环境模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.ERROR -> ModuleState.INACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 浏览器环境模块清理完成
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在清理视频文件模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已停止
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块清理完成
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在清理视频配置模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块清理完成
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在清理上传控制模块...
2025-08-02 04:54:09 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 所有上传已停止
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传工作线程已停止
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块清理完成
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在清理HubStudio API模块...
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] HubStudio API模块清理完成
2025-08-02 04:54:10 | [32mINFO[0m | HubStudio.App:shutdown:366 - 应用程序已关闭
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:56:48 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接测试异常: HTTPConnectionPool(host='127.0.0.1', port=50325): Max retries exceeded with url: /api/v1/browser/active (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002007F0D4640>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] API连接失败，模块初始化失败
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INITIALIZING -> ModuleState.ERROR
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.App:_handle_global_error:301 - 模块错误 [BrowserEnvironment]: API连接失败，模块初始化失败
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在初始化视频文件模块...
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已启动
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块初始化成功
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在初始化视频配置模块...
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块初始化成功
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在初始化上传控制模块...
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 已启动 3 个上传工作线程
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块初始化成功
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在初始化HubStudio API模块...
2025-08-02 04:57:02 | [33mWARNING[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] API连接失败，模块以离线模式启动
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.INITIALIZING -> ModuleState.ACTIVE
2025-08-02 04:57:02 | [33mWARNING[0m | HubStudio.App:_initialize_modules:172 - 以下模块初始化失败: ['BrowserEnvironment']
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_initialize_modules:178 - 模块状态摘要: {'total_modules': 5, 'active_modules': 4, 'error_modules': 1, 'inactive_modules': 0, 'modules': {'BrowserEnvironment': {'module_name': 'BrowserEnvironment', 'state': 'error', 'stats': {'total_environments': 0, 'online_environments': 0, 'busy_environments': 0, 'last_refresh_time': None, 'api_connection_status': False}, 'environments_count': 0, 'selected_count': 0, 'monitoring_active': False, 'api_base_url': 'http://127.0.0.1:50325'}, 'VideoFile': {'module_name': 'VideoFile', 'state': 'active', 'stats': {'total_files': 0, 'valid_files': 0, 'invalid_files': 0, 'total_size': 0, 'selected_count': 0, 'selected_size': 0}, 'supported_formats': ['.webm', '.m2ts', '.flv', '.mp4', '.3gp', '.ts', '.ogv', '.m4v', '.mkv', '.mts', '.mov', '.avi', '.wmv'], 'file_size_limits': {'min_size': 1048576, 'max_size': 137438953472}, 'validation_active': True, 'validation_queue_size': 0}, 'VideoConfig': {'module_name': 'VideoConfig', 'state': 'active', 'stats': {'total_templates': 1, 'configured_videos': 0, 'current_template': '默认模板'}, 'current_template': '默认模板', 'available_templates': ['默认模板'], 'privacy_levels': ['public', 'unlisted', 'private'], 'categories': ['1', '2', '10', '15', '17', '19', '20', '22', '23', '24', '25', '26', '27', '28', '29']}, 'UploadControl': {'module_name': 'UploadControl', 'state': 'active', 'stats': {'total_sessions': 0, 'active_sessions': 0, 'total_tasks': 0, 'completed_tasks': 0, 'failed_tasks': 0, 'active_uploads': 0, 'upload_speed': 0.0, 'eta': None}, 'max_concurrent': 3, 'current_session': None, 'active_tasks_count': 0, 'queue_size': 0, 'workers_active': True}, 'HubStudioAPI': {'module_name': 'HubStudioAPI', 'state': 'active', 'connected': False, 'credentials_configured': False, 'server_url': None, 'upload_stats': {'total_uploads': 0, 'successful_uploads': 0, 'failed_uploads': 0, 'total_upload_time': 0.0, 'average_upload_time': 0.0, 'last_upload_time': None}, 'monitoring_active': False, 'last_ping_time': 0}}}
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_setup_module_communication:211 - 模块间通信已设置
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_initialize:85 - 应用程序初始化完成
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:run:325 - 正在启动GUI界面...
2025-08-02 04:57:02 | [31mERROR[0m | HubStudio.App:run:340 - 运行应用程序失败: invalid command name "tkdnd::drop_target"
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\project\dianshang\src\main.py", line 328, in run
    self.main_window = MainWindow(
                       ~~~~~~~~~~^
        app=self,
        ^^^^^^^^^
        config_manager=self.config_manager,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        module_manager=self.module_manager
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 270, in __init__
    self._setup_ui()
    ~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\main_window.py", line 314, in _setup_ui
    self.file_selector = FileSelectorComponent(
                         ~~~~~~~~~~~~~~~~~~~~~^
        self.main_content,
        ^^^^^^^^^^^^^^^^^^
        on_files_changed=self._on_files_changed
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 200, in __init__
    self._setup_drag_drop()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 286, in _setup_drag_drop
    self.drop_target = FileDropTarget(self.list_frame, self._on_files_dropped)
                       ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 22, in __init__
    self._setup_drop_target()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\project\dianshang\src\gui\components\file_selector.py", line 31, in _setup_drop_target
    self.widget.drop_target_register(tkdnd.DND_FILES)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\project\dianshang\.venv\Lib\site-packages\tkinterdnd2\TkinterDnD.py", line 244, in drop_target_register
    self.tk.call('tkdnd::drop_target', 'register', self._w, dndtypes)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: invalid command name "tkdnd::drop_target"
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:shutdown:350 - 正在关闭应用程序...
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在清理浏览器环境模块...
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.ERROR -> ModuleState.INACTIVE
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 浏览器环境模块清理完成
2025-08-02 04:57:02 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 正在清理视频文件模块...
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 文件验证工作线程已停止
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoFile]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.VideoFile:_log:129 - [VideoFile] 视频文件模块清理完成
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 正在清理视频配置模块...
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [VideoConfig]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.VideoConfig:_log:129 - [VideoConfig] 视频配置模块清理完成
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 正在清理上传控制模块...
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 所有上传已停止
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传工作线程已停止
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [UploadControl]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.UploadControl:_log:129 - [UploadControl] 上传控制模块清理完成
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] 正在清理HubStudio API模块...
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [HubStudioAPI]: ModuleState.ACTIVE -> ModuleState.INACTIVE
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.HubStudioAPI:_log:129 - [HubStudioAPI] HubStudio API模块清理完成
2025-08-02 04:57:03 | [32mINFO[0m | HubStudio.App:shutdown:366 - 应用程序已关闭
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_initialize:65 - 正在启动 HubStudio Video Uploader v2.0.0
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_setup_logging:106 - 日志系统已设置，级别: INFO
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: BrowserEnvironment
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoFile
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: VideoConfig
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: UploadControl
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_register_modules:152 - 模块已注册: HubStudioAPI
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_initialize_modules:163 - 正在初始化功能模块...
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.App:_handle_module_state_change:316 - 模块状态变化 [BrowserEnvironment]: ModuleState.INACTIVE -> ModuleState.INITIALIZING
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在初始化浏览器环境模块...
2025-08-02 04:57:36 | [32mINFO[0m | HubStudio.BrowserEnvironment:_log:129 - [BrowserEnvironment] 正在测试API连接: http://127.0.0.1:50325
