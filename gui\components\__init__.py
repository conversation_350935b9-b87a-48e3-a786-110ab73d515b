"""
GUI组件模块 - 包含所有用户界面组件
"""

from .main_window import MainWindow
from .control_panel import ControlPanel
from .video_panel import VideoPanel
from .config_panel import ConfigPanel
from .log_panel import LogPanel

__all__ = [
    'MainWindow',
    'ControlPanel', 
    'VideoPanel',
    'ConfigPanel',
    'LogPanel'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'HubStudio Team'
__description__ = 'GUI组件模块，提供完整的用户界面组件'

# 组件注册表
COMPONENT_REGISTRY = {
    'main_window': MainWindow,
    'control_panel': ControlPanel,
    'video_panel': VideoPanel,
    'config_panel': ConfigPanel,
    'log_panel': LogPanel
}

def get_component(component_name: str):
    """
    根据组件名称获取组件类
    
    Args:
        component_name: 组件名称
        
    Returns:
        组件类或None
    """
    return COMPONENT_REGISTRY.get(component_name)

def list_components():
    """
    列出所有可用的组件
    
    Returns:
        组件名称列表
    """
    return list(COMPONENT_REGISTRY.keys())