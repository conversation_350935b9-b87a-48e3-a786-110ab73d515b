# HubStudio 视频上传工具重构实施指南

## 1. 准备工作

### 1.1 环境准备
```bash
# 创建新的虚拟环境
python -m venv venv_new
source venv_new/bin/activate  # Linux/Mac
# 或
venv_new\Scripts\activate  # Windows

# 安装基础依赖
pip install --upgrade pip
pip install wheel setuptools
```

### 1.2 代码备份
```bash
# 创建当前代码的备份
git branch backup-original
git checkout -b refactor-main

# 或者直接复制目录
cp -r dianshang dianshang_backup
```

### 1.3 新项目结构创建
```bash
mkdir -p src/{domain,application,infrastructure,presentation}
mkdir -p src/domain/{entities,value_objects,repositories}
mkdir -p src/application/{services,dto,interfaces}
mkdir -p src/infrastructure/{database,external_apis,logging,config}
mkdir -p src/presentation/{gui,cli,web}
mkdir -p tests/{unit,integration,e2e}
mkdir -p docs/{api,user,developer}
mkdir -p scripts
mkdir -p requirements
```

## 2. 第一阶段实施：基础设施重构

### 2.1 依赖管理重构

#### 创建新的依赖文件
```bash
# requirements/base.txt
fastapi>=0.104.0
pydantic>=2.5.0
sqlalchemy>=2.0.0
alembic>=1.13.0
loguru>=0.7.0
click>=8.1.0
python-dotenv>=1.0.0
cryptography>=41.0.0
aiofiles>=23.0.0
httpx>=0.25.0

# requirements/gui.txt
-r base.txt
customtkinter>=5.2.0
pillow>=10.0.0
tkinter-tooltip>=2.0.0

# requirements/dev.txt
-r gui.txt
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.7.0
pre-commit>=3.5.0

# requirements/prod.txt
-r gui.txt
gunicorn>=21.2.0
```

#### 安装新依赖
```bash
pip install -r requirements/dev.txt
```

### 2.2 配置系统重构

#### 创建配置模块
```python
# src/infrastructure/config/settings.py
from pydantic import BaseSettings, Field
from typing import Optional
import os

class DatabaseSettings(BaseSettings):
    url: str = Field(default="sqlite:///./hubstudio.db")
    echo: bool = Field(default=False)
    
    class Config:
        env_prefix = "DB_"

class HubStudioAPISettings(BaseSettings):
    base_url: str = Field(default="http://127.0.0.1:50325")
    api_id: Optional[str] = Field(default=None)
    api_secret: Optional[str] = Field(default=None)
    timeout: int = Field(default=30)
    retry_count: int = Field(default=3)
    
    class Config:
        env_prefix = "HUBSTUDIO_"

class UploadSettings(BaseSettings):
    max_concurrent: int = Field(default=3)
    chunk_size: int = Field(default=1048576)  # 1MB
    retry_delay: int = Field(default=5)
    max_file_size: int = Field(default=2147483648)  # 2GB
    
    class Config:
        env_prefix = "UPLOAD_"

class Settings(BaseSettings):
    app_name: str = "HubStudio Uploader"
    version: str = "2.0.0"
    debug: bool = Field(default=False)
    
    database: DatabaseSettings = DatabaseSettings()
    hubstudio_api: HubStudioAPISettings = HubStudioAPISettings()
    upload: UploadSettings = UploadSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 全局设置实例
settings = Settings()
```

#### 创建环境变量文件
```bash
# .env
DEBUG=true
DB_URL=sqlite:///./hubstudio.db
HUBSTUDIO_BASE_URL=http://127.0.0.1:50325
UPLOAD_MAX_CONCURRENT=3
```

### 2.3 日志系统重构

#### 创建日志配置
```python
# src/infrastructure/logging/logger.py
from loguru import logger
import sys
from pathlib import Path

def setup_logging(debug: bool = False):
    """设置日志系统"""
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出
    log_level = "DEBUG" if debug else "INFO"
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 文件输出
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logger.add(
        log_dir / "hubstudio.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    # 错误日志单独文件
    logger.add(
        log_dir / "errors.log",
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 week",
        retention="12 weeks"
    )
    
    return logger

# 创建全局logger实例
app_logger = setup_logging()
```

### 2.4 数据库设计

#### 创建数据模型
```python
# src/infrastructure/database/models.py
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, Enum, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class VideoStatus(enum.Enum):
    PENDING = "pending"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    FAILED = "failed"

class PrivacyLevel(enum.Enum):
    PUBLIC = "public"
    UNLISTED = "unlisted"
    PRIVATE = "private"

class VideoModel(Base):
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    file_path = Column(String, nullable=False)
    title = Column(String, nullable=False)
    description = Column(Text)
    tags = Column(Text)  # JSON string
    privacy = Column(Enum(PrivacyLevel), default=PrivacyLevel.PRIVATE)
    category = Column(String, default="22")
    status = Column(Enum(VideoStatus), default=VideoStatus.PENDING)
    upload_progress = Column(Float, default=0.0)
    error_message = Column(Text)
    file_size = Column(Integer)
    duration = Column(Float)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class UploadSessionModel(Base):
    __tablename__ = "upload_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, unique=True, index=True)
    total_videos = Column(Integer, default=0)
    completed_videos = Column(Integer, default=0)
    failed_videos = Column(Integer, default=0)
    overall_progress = Column(Float, default=0.0)
    status = Column(String, default="created")
    config_json = Column(Text)  # JSON string
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class BrowserEnvironmentModel(Base):
    __tablename__ = "browser_environments"
    
    id = Column(Integer, primary_key=True, index=True)
    env_id = Column(String, unique=True, index=True)
    name = Column(String, nullable=False)
    browser_type = Column(String, default="chrome")
    status = Column(String, default="inactive")
    proxy_info = Column(Text)  # JSON string
    is_active = Column(Boolean, default=False)
    last_used = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

#### 创建数据库连接
```python
# src/infrastructure/database/connection.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator

from .models import Base
from ..config.settings import settings

# 创建数据库引擎
engine = create_engine(
    settings.database.url,
    echo=settings.database.echo,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database.url else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """创建数据库表"""
    Base.metadata.create_all(bind=engine)

@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

def get_db() -> Generator[Session, None, None]:
    """依赖注入用的数据库会话获取器"""
    with get_db_session() as session:
        yield session
```

## 3. 第二阶段实施：领域模型重构

### 3.1 创建领域实体

#### 视频实体
```python
# src/domain/entities/video.py
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional
from datetime import datetime
import uuid

from ..value_objects.video_status import VideoStatus
from ..value_objects.privacy_level import PrivacyLevel

@dataclass
class Video:
    file_path: Path
    title: str
    description: str
    tags: List[str] = field(default_factory=list)
    privacy: PrivacyLevel = PrivacyLevel.PRIVATE
    category: str = "22"
    status: VideoStatus = VideoStatus.PENDING
    upload_progress: float = 0.0
    error_message: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.file_path.exists():
            raise ValueError(f"视频文件不存在: {self.file_path}")
        
        if not self.title.strip():
            raise ValueError("视频标题不能为空")
    
    @property
    def file_size(self) -> int:
        """获取文件大小（字节）"""
        return self.file_path.stat().st_size if self.file_path.exists() else 0
    
    @property
    def file_name(self) -> str:
        """获取文件名"""
        return self.file_path.name
    
    @property
    def file_extension(self) -> str:
        """获取文件扩展名"""
        return self.file_path.suffix.lower()
    
    def update_progress(self, progress: float):
        """更新上传进度"""
        if not 0 <= progress <= 100:
            raise ValueError("进度值必须在0-100之间")
        self.upload_progress = progress
    
    def mark_as_uploading(self):
        """标记为上传中"""
        self.status = VideoStatus.UPLOADING
        self.error_message = None
    
    def mark_as_completed(self):
        """标记为上传完成"""
        self.status = VideoStatus.COMPLETED
        self.upload_progress = 100.0
        self.error_message = None
    
    def mark_as_failed(self, error_message: str):
        """标记为上传失败"""
        self.status = VideoStatus.FAILED
        self.error_message = error_message
    
    def is_valid_video_file(self) -> bool:
        """检查是否为有效的视频文件"""
        valid_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
        return self.file_extension in valid_extensions
```

### 3.2 创建值对象

#### 视频状态值对象
```python
# src/domain/value_objects/video_status.py
from enum import Enum

class VideoStatus(Enum):
    PENDING = "pending"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    FAILED = "failed"
    
    def __str__(self):
        return self.value
    
    @property
    def display_name(self) -> str:
        """获取显示名称"""
        display_names = {
            self.PENDING: "等待上传",
            self.UPLOADING: "上传中",
            self.COMPLETED: "上传完成",
            self.FAILED: "上传失败"
        }
        return display_names[self]
    
    @property
    def is_final_state(self) -> bool:
        """是否为最终状态"""
        return self in (self.COMPLETED, self.FAILED)
    
    @property
    def can_retry(self) -> bool:
        """是否可以重试"""
        return self == self.FAILED
```

### 3.3 创建仓储接口

#### 视频仓储接口
```python
# src/domain/repositories/video_repository.py
from abc import ABC, abstractmethod
from typing import List, Optional
from ..entities.video import Video

class VideoRepository(ABC):
    """视频仓储接口"""
    
    @abstractmethod
    async def save(self, video: Video) -> Video:
        """保存视频"""
        pass
    
    @abstractmethod
    async def find_by_id(self, video_id: int) -> Optional[Video]:
        """根据ID查找视频"""
        pass
    
    @abstractmethod
    async def find_by_file_path(self, file_path: str) -> Optional[Video]:
        """根据文件路径查找视频"""
        pass
    
    @abstractmethod
    async def find_all(self) -> List[Video]:
        """查找所有视频"""
        pass
    
    @abstractmethod
    async def find_by_status(self, status: VideoStatus) -> List[Video]:
        """根据状态查找视频"""
        pass
    
    @abstractmethod
    async def update(self, video: Video) -> Video:
        """更新视频"""
        pass
    
    @abstractmethod
    async def delete(self, video_id: int) -> bool:
        """删除视频"""
        pass
    
    @abstractmethod
    async def delete_all(self) -> int:
        """删除所有视频"""
        pass
```

## 4. 第三阶段实施：应用服务重构

### 4.1 创建应用服务

#### 视频上传服务
```python
# src/application/services/upload_service.py
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
import uuid

from ...domain.entities.video import Video
from ...domain.repositories.video_repository import VideoRepository
from ...infrastructure.external_apis.hubstudio_client import HubStudioClient
from ...infrastructure.logging.logger import app_logger
from ..dto.upload_request import UploadRequest
from ..dto.upload_result import UploadResult

class UploadService:
    """视频上传服务"""
    
    def __init__(self, 
                 video_repository: VideoRepository,
                 hubstudio_client: HubStudioClient,
                 max_concurrent: int = 3):
        self.video_repository = video_repository
        self.hubstudio_client = hubstudio_client
        self.max_concurrent = max_concurrent
        self.logger = app_logger
        
    async def upload_videos(self, request: UploadRequest) -> UploadResult:
        """批量上传视频"""
        session_id = str(uuid.uuid4())
        self.logger.info(f"开始上传会话: {session_id}, 视频数量: {len(request.videos)}")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # 保存视频到数据库
        saved_videos = []
        for video_data in request.videos:
            video = Video(
                file_path=Path(video_data.file_path),
                title=video_data.title,
                description=video_data.description,
                tags=video_data.tags,
                privacy=video_data.privacy,
                category=video_data.category
            )
            saved_video = await self.video_repository.save(video)
            saved_videos.append(saved_video)
        
        # 创建上传任务
        tasks = [
            self._upload_single_video(video, semaphore, session_id)
            for video in saved_videos
        ]
        
        # 执行上传
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        successful_uploads = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        failed_uploads = len(results) - successful_uploads
        
        self.logger.info(f"上传会话完成: {session_id}, 成功: {successful_uploads}, 失败: {failed_uploads}")
        
        return UploadResult(
            session_id=session_id,
            total_videos=len(saved_videos),
            successful_uploads=successful_uploads,
            failed_uploads=failed_uploads,
            results=results
        )
    
    async def _upload_single_video(self, video: Video, semaphore: asyncio.Semaphore, session_id: str) -> Dict[str, Any]:
        """上传单个视频"""
        async with semaphore:
            try:
                self.logger.info(f"开始上传视频: {video.file_name}")
                
                # 标记为上传中
                video.mark_as_uploading()
                await self.video_repository.update(video)
                
                # 执行上传
                upload_result = await self.hubstudio_client.upload_video(
                    video_path=video.file_path,
                    title=video.title,
                    description=video.description,
                    tags=video.tags,
                    privacy=video.privacy.value,
                    category=video.category,
                    progress_callback=lambda p: self._update_progress(video, p)
                )
                
                if upload_result.success:
                    video.mark_as_completed()
                    self.logger.info(f"视频上传成功: {video.file_name}")
                else:
                    video.mark_as_failed(upload_result.error_message)
                    self.logger.error(f"视频上传失败: {video.file_name}, 错误: {upload_result.error_message}")
                
                await self.video_repository.update(video)
                
                return {
                    'video_id': video.id,
                    'file_name': video.file_name,
                    'success': upload_result.success,
                    'error_message': upload_result.error_message
                }
                
            except Exception as e:
                self.logger.error(f"上传视频异常: {video.file_name}, 错误: {e}")
                video.mark_as_failed(str(e))
                await self.video_repository.update(video)
                
                return {
                    'video_id': video.id,
                    'file_name': video.file_name,
                    'success': False,
                    'error_message': str(e)
                }
    
    async def _update_progress(self, video: Video, progress: float):
        """更新上传进度"""
        try:
            video.update_progress(progress)
            await self.video_repository.update(video)
        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")
```

## 5. 测试策略

### 5.1 单元测试示例
```python
# tests/unit/test_video_entity.py
import pytest
from pathlib import Path
from src.domain.entities.video import Video
from src.domain.value_objects.video_status import VideoStatus
from src.domain.value_objects.privacy_level import PrivacyLevel

class TestVideoEntity:
    def test_create_video_with_valid_data(self, tmp_path):
        # 创建临时视频文件
        video_file = tmp_path / "test_video.mp4"
        video_file.write_text("fake video content")
        
        video = Video(
            file_path=video_file,
            title="测试视频",
            description="这是一个测试视频"
        )
        
        assert video.title == "测试视频"
        assert video.description == "这是一个测试视频"
        assert video.status == VideoStatus.PENDING
        assert video.privacy == PrivacyLevel.PRIVATE
        assert video.file_name == "test_video.mp4"
        assert video.file_extension == ".mp4"
    
    def test_create_video_with_nonexistent_file(self):
        with pytest.raises(ValueError, match="视频文件不存在"):
            Video(
                file_path=Path("/nonexistent/file.mp4"),
                title="测试视频",
                description="描述"
            )
    
    def test_create_video_with_empty_title(self, tmp_path):
        video_file = tmp_path / "test_video.mp4"
        video_file.write_text("fake video content")
        
        with pytest.raises(ValueError, match="视频标题不能为空"):
            Video(
                file_path=video_file,
                title="",
                description="描述"
            )
    
    def test_update_progress(self, tmp_path):
        video_file = tmp_path / "test_video.mp4"
        video_file.write_text("fake video content")
        
        video = Video(
            file_path=video_file,
            title="测试视频",
            description="描述"
        )
        
        video.update_progress(50.0)
        assert video.upload_progress == 50.0
        
        with pytest.raises(ValueError, match="进度值必须在0-100之间"):
            video.update_progress(150.0)
    
    def test_status_transitions(self, tmp_path):
        video_file = tmp_path / "test_video.mp4"
        video_file.write_text("fake video content")
        
        video = Video(
            file_path=video_file,
            title="测试视频",
            description="描述"
        )
        
        # 测试状态转换
        video.mark_as_uploading()
        assert video.status == VideoStatus.UPLOADING
        assert video.error_message is None
        
        video.mark_as_completed()
        assert video.status == VideoStatus.COMPLETED
        assert video.upload_progress == 100.0
        
        video.mark_as_failed("上传失败")
        assert video.status == VideoStatus.FAILED
        assert video.error_message == "上传失败"
```

### 5.2 集成测试示例
```python
# tests/integration/test_upload_service.py
import pytest
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, Mock

from src.application.services.upload_service import UploadService
from src.application.dto.upload_request import UploadRequest, VideoUploadData
from src.domain.value_objects.privacy_level import PrivacyLevel

@pytest.mark.asyncio
class TestUploadService:
    async def test_upload_videos_success(self, tmp_path):
        # 创建模拟依赖
        video_repository = AsyncMock()
        hubstudio_client = AsyncMock()
        
        # 创建测试文件
        video_file = tmp_path / "test_video.mp4"
        video_file.write_text("fake video content")
        
        # 配置模拟返回值
        video_repository.save.return_value = Mock(id=1, file_name="test_video.mp4")
        hubstudio_client.upload_video.return_value = Mock(success=True, error_message=None)
        
        # 创建服务实例
        service = UploadService(video_repository, hubstudio_client, max_concurrent=1)
        
        # 创建上传请求
        request = UploadRequest(
            videos=[
                VideoUploadData(
                    file_path=str(video_file),
                    title="测试视频",
                    description="测试描述",
                    tags=["测试"],
                    privacy=PrivacyLevel.PRIVATE,
                    category="22"
                )
            ]
        )
        
        # 执行上传
        result = await service.upload_videos(request)
        
        # 验证结果
        assert result.total_videos == 1
        assert result.successful_uploads == 1
        assert result.failed_uploads == 0
        
        # 验证调用
        video_repository.save.assert_called_once()
        hubstudio_client.upload_video.assert_called_once()
```

## 6. 部署和运行

### 6.1 开发环境运行
```bash
# 激活虚拟环境
source venv_new/bin/activate

# 安装依赖
pip install -r requirements/dev.txt

# 初始化数据库
python -c "from src.infrastructure.database.connection import create_tables; create_tables()"

# 运行GUI应用
python -m src.presentation.gui.main

# 或运行CLI应用
python -m src.presentation.cli.main --help
```

### 6.2 生产环境部署
```bash
# 安装生产依赖
pip install -r requirements/prod.txt

# 设置环境变量
export DEBUG=false
export DB_URL=sqlite:///./production.db

# 运行应用
python -m src.presentation.gui.main
```

## 7. 最佳实践建议

### 7.1 代码质量
- 使用类型注解
- 编写单元测试
- 使用代码格式化工具（black, isort）
- 使用静态分析工具（mypy, flake8）

### 7.2 错误处理
- 使用自定义异常类
- 记录详细的错误日志
- 提供用户友好的错误消息
- 实现重试机制

### 7.3 性能优化
- 使用异步编程
- 实现连接池
- 添加缓存机制
- 监控性能指标

### 7.4 安全考虑
- 加密敏感配置
- 验证用户输入
- 使用HTTPS连接
- 定期更新依赖
