"""
并发控制管理器 - 精确控制并发数和浏览器环境数量
"""

import threading
import time
from queue import Queue, Empty, PriorityQueue
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import Thread<PERSON>oolExecutor, Future
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


@dataclass
class Task:
    """任务数据类"""
    task_id: str
    task_func: Callable
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    created_at: float = field(default_factory=time.time)
    started_at: float = 0.0
    completed_at: float = 0.0
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Exception = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: float = 300.0  # 5分钟超时
    metadata: Dict = field(default_factory=dict)
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        return self.priority.value < other.priority.value


@dataclass
class BrowserResource:
    """浏览器资源"""
    resource_id: str
    environment_id: str
    browser_instance: Any = None
    is_busy: bool = False
    created_at: float = field(default_factory=time.time)
    last_used_at: float = field(default_factory=time.time)
    task_count: int = 0
    max_tasks: int = 10  # 单个浏览器最大任务数
    
    def is_available(self) -> bool:
        """检查资源是否可用"""
        return not self.is_busy and self.task_count < self.max_tasks


class ConcurrencyManager:
    """并发控制管理器"""
    
    def __init__(self, max_concurrent: int = 3, max_browsers: int = 5, 
                 state_manager=None, logger=None):
        self.max_concurrent = max_concurrent
        self.max_browsers = max_browsers
        self.state_manager = state_manager
        self.logger = logger
        
        # 任务管理
        self._task_queue = PriorityQueue()
        self._active_tasks = {}  # task_id -> Task
        self._completed_tasks = {}  # task_id -> Task
        self._task_futures = {}  # task_id -> Future
        
        # 浏览器资源管理
        self._browser_resources = {}  # resource_id -> BrowserResource
        self._resource_lock = threading.RLock()
        
        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=max_concurrent, 
                                          thread_name_prefix="ConcurrencyManager")
        
        # 控制标志
        self._running = False
        self._paused = False
        self._shutdown = False
        
        # 统计信息
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'active_browsers': 0,
            'total_browsers_created': 0
        }
        
        # 锁
        self._stats_lock = threading.Lock()
        self._task_lock = threading.RLock()
        
        # 监控线程
        self._monitor_thread = None
        
        self._log('info', '并发管理器初始化完成')
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[并发管理器] {message}")
    
    def start(self):
        """启动并发管理器"""
        if self._running:
            return
        
        self._running = True
        self._shutdown = False
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self._log('info', '并发管理器已启动')
    
    def stop(self):
        """停止并发管理器"""
        if not self._running:
            return
        
        self._running = False
        self._shutdown = True
        
        # 取消所有待处理任务
        self._cancel_pending_tasks()
        
        # 等待活动任务完成
        self._wait_for_active_tasks()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        # 清理浏览器资源
        self._cleanup_browser_resources()
        
        self._log('info', '并发管理器已停止')
    
    def pause(self):
        """暂停任务处理"""
        self._paused = True
        self._log('info', '并发管理器已暂停')
    
    def resume(self):
        """恢复任务处理"""
        self._paused = False
        self._log('info', '并发管理器已恢复')
    
    def submit_task(self, task_func: Callable, *args, priority: TaskPriority = TaskPriority.NORMAL,
                   timeout: float = 300.0, max_retries: int = 3, metadata: Dict = None, **kwargs) -> str:
        """提交任务"""
        if self._shutdown:
            raise RuntimeError("并发管理器已关闭")
        
        task_id = str(uuid.uuid4())
        task = Task(
            task_id=task_id,
            task_func=task_func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries,
            metadata=metadata or {}
        )
        
        with self._task_lock:
            self._task_queue.put(task)
            self._stats['total_tasks'] += 1
        
        self._log('info', f'任务已提交: {task_id}')
        return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._task_lock:
            # 检查是否在活动任务中
            if task_id in self._active_tasks:
                task = self._active_tasks[task_id]
                task.status = TaskStatus.CANCELLED
                
                # 取消Future
                if task_id in self._task_futures:
                    future = self._task_futures[task_id]
                    future.cancel()
                
                self._move_task_to_completed(task_id)
                self._stats['cancelled_tasks'] += 1
                
                self._log('info', f'任务已取消: {task_id}')
                return True
            
            # 检查是否在队列中（需要遍历队列）
            # 注意：PriorityQueue不支持直接删除，这里简化处理
            return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        with self._task_lock:
            if task_id in self._active_tasks:
                return self._active_tasks[task_id].status
            elif task_id in self._completed_tasks:
                return self._completed_tasks[task_id].status
            else:
                return None
    
    def get_task_result(self, task_id: str) -> Any:
        """获取任务结果"""
        with self._task_lock:
            if task_id in self._completed_tasks:
                task = self._completed_tasks[task_id]
                if task.status == TaskStatus.COMPLETED:
                    return task.result
                elif task.status == TaskStatus.FAILED:
                    raise task.error
            return None
    
    def acquire_browser_resource(self, environment_id: str, browser_instance: Any) -> str:
        """获取浏览器资源"""
        with self._resource_lock:
            if len(self._browser_resources) >= self.max_browsers:
                # 尝试清理空闲资源
                self._cleanup_idle_resources()
                
                if len(self._browser_resources) >= self.max_browsers:
                    raise RuntimeError(f"浏览器资源已达上限: {self.max_browsers}")
            
            resource_id = str(uuid.uuid4())
            resource = BrowserResource(
                resource_id=resource_id,
                environment_id=environment_id,
                browser_instance=browser_instance
            )
            
            self._browser_resources[resource_id] = resource
            self._stats['active_browsers'] = len(self._browser_resources)
            self._stats['total_browsers_created'] += 1
            
            self._log('info', f'浏览器资源已获取: {resource_id}')
            return resource_id
    
    def release_browser_resource(self, resource_id: str):
        """释放浏览器资源"""
        with self._resource_lock:
            if resource_id in self._browser_resources:
                resource = self._browser_resources[resource_id]
                
                # 清理浏览器实例
                try:
                    if hasattr(resource.browser_instance, 'quit'):
                        resource.browser_instance.quit()
                except Exception as e:
                    self._log('warning', f'清理浏览器实例失败: {e}')
                
                del self._browser_resources[resource_id]
                self._stats['active_browsers'] = len(self._browser_resources)
                
                self._log('info', f'浏览器资源已释放: {resource_id}')
    
    def get_available_browser_resource(self) -> Optional[str]:
        """获取可用的浏览器资源"""
        with self._resource_lock:
            for resource_id, resource in self._browser_resources.items():
                if resource.is_available():
                    resource.is_busy = True
                    resource.last_used_at = time.time()
                    return resource_id
            return None
    
    def mark_browser_resource_free(self, resource_id: str):
        """标记浏览器资源为空闲"""
        with self._resource_lock:
            if resource_id in self._browser_resources:
                resource = self._browser_resources[resource_id]
                resource.is_busy = False
                resource.task_count += 1
                resource.last_used_at = time.time()
    
    def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                if not self._paused and not self._task_queue.empty():
                    self._process_pending_tasks()
                
                # 清理超时任务
                self._cleanup_timeout_tasks()
                
                # 更新状态
                self._update_state()
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                self._log('error', f'监控循环错误: {e}')
                time.sleep(5)  # 错误时等待更长时间
    
    def _process_pending_tasks(self):
        """处理待处理任务"""
        with self._task_lock:
            # 检查是否有空闲的执行器
            active_count = len(self._active_tasks)
            if active_count >= self.max_concurrent:
                return
            
            try:
                # 获取下一个任务
                task = self._task_queue.get_nowait()
                
                # 提交到线程池执行
                future = self._executor.submit(self._execute_task, task)
                
                # 记录任务信息
                task.status = TaskStatus.RUNNING
                task.started_at = time.time()
                self._active_tasks[task.task_id] = task
                self._task_futures[task.task_id] = future
                
                self._log('info', f'任务开始执行: {task.task_id}')
                
            except Empty:
                pass  # 队列为空
            except Exception as e:
                self._log('error', f'处理待处理任务失败: {e}')
    
    def _execute_task(self, task: Task) -> Any:
        """执行任务"""
        try:
            # 执行任务函数
            result = task.task_func(*task.args, **task.kwargs)
            
            # 更新任务状态
            with self._task_lock:
                task.result = result
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                self._move_task_to_completed(task.task_id)
                self._stats['completed_tasks'] += 1
            
            self._log('info', f'任务执行完成: {task.task_id}')
            return result
            
        except Exception as e:
            # 处理任务失败
            with self._task_lock:
                task.error = e
                task.retry_count += 1
                
                # 检查是否需要重试
                if task.retry_count <= task.max_retries:
                    task.status = TaskStatus.PENDING
                    self._task_queue.put(task)  # 重新加入队列
                    self._log('warning', f'任务执行失败，准备重试 ({task.retry_count}/{task.max_retries}): {task.task_id}')
                else:
                    task.status = TaskStatus.FAILED
                    task.completed_at = time.time()
                    self._move_task_to_completed(task.task_id)
                    self._stats['failed_tasks'] += 1
                    self._log('error', f'任务执行失败: {task.task_id}, 错误: {e}')
            
            raise e
    
    def _move_task_to_completed(self, task_id: str):
        """将任务移动到已完成列表"""
        if task_id in self._active_tasks:
            task = self._active_tasks[task_id]
            self._completed_tasks[task_id] = task
            del self._active_tasks[task_id]
            
            # 清理Future引用
            if task_id in self._task_futures:
                del self._task_futures[task_id]
    
    def _cleanup_timeout_tasks(self):
        """清理超时任务"""
        current_time = time.time()
        timeout_tasks = []
        
        with self._task_lock:
            for task_id, task in self._active_tasks.items():
                if current_time - task.started_at > task.timeout:
                    timeout_tasks.append(task_id)
        
        for task_id in timeout_tasks:
            self.cancel_task(task_id)
            self._log('warning', f'任务超时被取消: {task_id}')
    
    def _cleanup_idle_resources(self):
        """清理空闲资源"""
        current_time = time.time()
        idle_timeout = 300  # 5分钟空闲超时
        idle_resources = []
        
        with self._resource_lock:
            for resource_id, resource in self._browser_resources.items():
                if (not resource.is_busy and 
                    current_time - resource.last_used_at > idle_timeout):
                    idle_resources.append(resource_id)
        
        for resource_id in idle_resources:
            self.release_browser_resource(resource_id)
            self._log('info', f'清理空闲浏览器资源: {resource_id}')
    
    def _cleanup_browser_resources(self):
        """清理所有浏览器资源"""
        with self._resource_lock:
            resource_ids = list(self._browser_resources.keys())
            for resource_id in resource_ids:
                self.release_browser_resource(resource_id)
    
    def _cancel_pending_tasks(self):
        """取消所有待处理任务"""
        cancelled_count = 0
        while not self._task_queue.empty():
            try:
                task = self._task_queue.get_nowait()
                task.status = TaskStatus.CANCELLED
                self._completed_tasks[task.task_id] = task
                cancelled_count += 1
            except Empty:
                break
        
        self._stats['cancelled_tasks'] += cancelled_count
        self._log('info', f'已取消 {cancelled_count} 个待处理任务')
    
    def _wait_for_active_tasks(self, timeout: float = 30.0):
        """等待活动任务完成"""
        start_time = time.time()
        while self._active_tasks and time.time() - start_time < timeout:
            time.sleep(1)
        
        # 强制取消剩余任务
        remaining_tasks = list(self._active_tasks.keys())
        for task_id in remaining_tasks:
            self.cancel_task(task_id)
    
    def _update_state(self):
        """更新状态管理器"""
        if self.state_manager:
            try:
                stats = self.get_statistics()
                self.state_manager.batch_update({
                    'concurrency_stats': stats,
                    'active_tasks_count': len(self._active_tasks),
                    'pending_tasks_count': self._task_queue.qsize(),
                    'active_browsers_count': len(self._browser_resources)
                })
            except Exception as e:
                self._log('warning', f'更新状态失败: {e}')
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._stats_lock:
            return {
                'total_tasks': self._stats['total_tasks'],
                'completed_tasks': self._stats['completed_tasks'],
                'failed_tasks': self._stats['failed_tasks'],
                'cancelled_tasks': self._stats['cancelled_tasks'],
                'active_tasks': len(self._active_tasks),
                'pending_tasks': self._task_queue.qsize(),
                'active_browsers': self._stats['active_browsers'],
                'total_browsers_created': self._stats['total_browsers_created'],
                'max_concurrent': self.max_concurrent,
                'max_browsers': self.max_browsers,
                'is_running': self._running,
                'is_paused': self._paused
            }
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活动任务列表"""
        with self._task_lock:
            tasks = []
            for task in self._active_tasks.values():
                tasks.append({
                    'task_id': task.task_id,
                    'status': task.status.value,
                    'priority': task.priority.value,
                    'created_at': task.created_at,
                    'started_at': task.started_at,
                    'retry_count': task.retry_count,
                    'metadata': task.metadata
                })
            return tasks
    
    def get_browser_resources_info(self) -> List[Dict[str, Any]]:
        """获取浏览器资源信息"""
        with self._resource_lock:
            resources = []
            for resource in self._browser_resources.values():
                resources.append({
                    'resource_id': resource.resource_id,
                    'environment_id': resource.environment_id,
                    'is_busy': resource.is_busy,
                    'task_count': resource.task_count,
                    'created_at': resource.created_at,
                    'last_used_at': resource.last_used_at
                })
            return resources
    
    def set_max_concurrent(self, max_concurrent: int):
        """设置最大并发数"""
        if max_concurrent > 0:
            self.max_concurrent = max_concurrent
            self._log('info', f'最大并发数已设置为: {max_concurrent}')
    
    def set_max_browsers(self, max_browsers: int):
        """设置最大浏览器数"""
        if max_browsers > 0:
            self.max_browsers = max_browsers
            self._log('info', f'最大浏览器数已设置为: {max_browsers}')
    
    def clear_completed_tasks(self, keep_recent_hours: int = 1):
        """清理已完成的任务"""
        cutoff_time = time.time() - (keep_recent_hours * 3600)
        removed_count = 0
        
        with self._task_lock:
            task_ids_to_remove = []
            for task_id, task in self._completed_tasks.items():
                if task.completed_at < cutoff_time:
                    task_ids_to_remove.append(task_id)
            
            for task_id in task_ids_to_remove:
                del self._completed_tasks[task_id]
                removed_count += 1
        
        self._log('info', f'已清理 {removed_count} 个已完成的任务')
        return removed_count
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop()
        except Exception:
            pass