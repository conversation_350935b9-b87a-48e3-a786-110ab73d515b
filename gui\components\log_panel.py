"""
日志面板组件 - 负责显示系统运行日志和状态信息
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import datetime
from typing import Dict, Any, List, Optional


class LogPanel:
    """日志面板组件"""
    
    def __init__(self, parent, event_manager, logger):
        self.parent = parent
        self.event_manager = event_manager
        self.logger = logger
        
        # 日志队列和缓存
        self.log_queue = queue.Queue()
        self.log_buffer = []
        self.max_log_lines = 1000
        
        # 界面变量
        self.auto_scroll_var = tk.BooleanVar(value=True)
        self.show_debug_var = tk.BooleanVar(value=False)
        self.show_info_var = tk.BooleanVar(value=True)
        self.show_warning_var = tk.BooleanVar(value=True)
        self.show_error_var = tk.BooleanVar(value=True)
        
        # 控件引用
        self.log_text = None
        self.status_label = None
        self.progress_bar = None
        self.clear_btn = None
        self.export_btn = None
        self.search_entry = None
        
        # 日志级别颜色映射
        self.level_colors = {
            'DEBUG': 'gray',
            'INFO': 'black',
            'WARNING': 'orange',
            'ERROR': 'red',
            'CRITICAL': 'purple'
        }
        
        # 搜索相关
        self.search_results = []
        self.current_search_index = 0
        
        self.create_widgets()
        self.setup_events()
        self.start_log_processor()
    
    def create_widgets(self):
        """创建日志面板界面"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="系统日志", padding="15")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建日志显示区域
        self._create_log_display()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧按钮组
        left_frame = ttk.Frame(toolbar_frame)
        left_frame.pack(side=tk.LEFT)
        
        # 清空日志按钮
        self.clear_btn = ttk.Button(
            left_frame,
            text="清空日志",
            command=self._clear_logs
        )
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出日志按钮
        self.export_btn = ttk.Button(
            left_frame,
            text="导出日志",
            command=self._export_logs
        )
        self.export_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 暂停/恢复按钮
        self.pause_btn = ttk.Button(
            left_frame,
            text="暂停",
            command=self._toggle_pause
        )
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 日志级别过滤
        filter_frame = ttk.LabelFrame(left_frame, text="日志级别", padding="5")
        filter_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # 级别复选框
        ttk.Checkbutton(filter_frame, text="调试", variable=self.show_debug_var,
                       command=self._apply_filters).pack(side=tk.LEFT)
        ttk.Checkbutton(filter_frame, text="信息", variable=self.show_info_var,
                       command=self._apply_filters).pack(side=tk.LEFT)
        ttk.Checkbutton(filter_frame, text="警告", variable=self.show_warning_var,
                       command=self._apply_filters).pack(side=tk.LEFT)
        ttk.Checkbutton(filter_frame, text="错误", variable=self.show_error_var,
                       command=self._apply_filters).pack(side=tk.LEFT)
        
        # 右侧搜索组
        right_frame = ttk.Frame(toolbar_frame)
        right_frame.pack(side=tk.RIGHT)
        
        # 搜索框
        search_frame = ttk.LabelFrame(right_frame, text="搜索", padding="5")
        search_frame.pack(side=tk.RIGHT)
        
        self.search_entry = ttk.Entry(search_frame, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.search_entry.bind('<Return>', self._search_logs)
        
        search_btn = ttk.Button(search_frame, text="搜索", command=self._search_logs)
        search_btn.pack(side=tk.LEFT, padx=(0, 2))
        
        next_btn = ttk.Button(search_frame, text="下一个", command=self._search_next)
        next_btn.pack(side=tk.LEFT, padx=(0, 2))
        
        prev_btn = ttk.Button(search_frame, text="上一个", command=self._search_prev)
        prev_btn.pack(side=tk.LEFT)
        
        # 自动滚动选项
        auto_scroll_cb = ttk.Checkbutton(right_frame, text="自动滚动", 
                                        variable=self.auto_scroll_var)
        auto_scroll_cb.pack(side=tk.RIGHT, padx=(0, 10))
    
    def _create_log_display(self):
        """创建日志显示区域"""
        # 日志文本框架
        log_frame = ttk.Frame(self.main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本控件
        self.log_text = tk.Text(
            log_frame,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="white",
            fg="black",
            state=tk.DISABLED
        )
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        h_scrollbar = ttk.Scrollbar(log_frame, orient=tk.HORIZONTAL, command=self.log_text.xview)
        
        self.log_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.log_text.grid(row=0, column=0, sticky=tk.NSEW)
        v_scrollbar.grid(row=0, column=1, sticky=tk.NS)
        h_scrollbar.grid(row=1, column=0, sticky=tk.EW)
        
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        # 配置文本标签样式
        for level, color in self.level_colors.items():
            self.log_text.tag_configure(level.lower(), foreground=color)
        
        # 搜索高亮样式
        self.log_text.tag_configure("search_highlight", background="yellow")
        self.log_text.tag_configure("current_search", background="orange")
        
        # 绑定右键菜单
        self.log_text.bind("<Button-3>", self._show_context_menu)
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="就绪", font=("Arial", 9))
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 日志统计
        self.log_count_label = ttk.Label(status_frame, text="日志: 0 条", font=("Arial", 9))
        self.log_count_label.pack(side=tk.RIGHT, padx=(10, 0))
    
    def setup_events(self):
        """设置事件绑定"""
        if self.event_manager:
            # 订阅日志相关事件
            self.event_manager.subscribe('log_message', self._on_log_message)
            self.event_manager.subscribe('status_update', self._on_status_update)
            self.event_manager.subscribe('progress_update', self._on_progress_update)
            
            # 订阅系统事件
            self.event_manager.subscribe('upload_started', self._on_upload_started)
            self.event_manager.subscribe('upload_completed', self._on_upload_completed)
            self.event_manager.subscribe('upload_failed', self._on_upload_failed)
    
    def start_log_processor(self):
        """启动日志处理器"""
        self.log_processor_running = True
        self.paused = False
        
        def process_logs():
            while self.log_processor_running:
                try:
                    # 处理队列中的日志消息
                    while not self.log_queue.empty() and not self.paused:
                        log_entry = self.log_queue.get_nowait()
                        self._add_log_entry(log_entry)
                    
                    # 短暂休眠避免CPU占用过高
                    threading.Event().wait(0.1)
                    
                except Exception as e:
                    print(f"日志处理器错误: {e}")
        
        self.log_thread = threading.Thread(target=process_logs, daemon=True)
        self.log_thread.start()
    
    def _add_log_entry(self, log_entry: Dict[str, Any]):
        """添加日志条目到显示区域"""
        try:
            level = log_entry.get('level', 'INFO')
            message = log_entry.get('message', '')
            timestamp = log_entry.get('timestamp', datetime.datetime.now())
            
            # 检查级别过滤
            if not self._should_show_level(level):
                return
            
            # 格式化日志消息
            formatted_message = f"[{timestamp.strftime('%H:%M:%S')}] [{level}] {message}\n"
            
            # 添加到缓存
            self.log_buffer.append({
                'timestamp': timestamp,
                'level': level,
                'message': message,
                'formatted': formatted_message
            })
            
            # 限制缓存大小
            if len(self.log_buffer) > self.max_log_lines:
                self.log_buffer.pop(0)
            
            # 更新界面（在主线程中执行）
            self.parent.after(0, self._update_log_display, formatted_message, level.lower())
            
        except Exception as e:
            print(f"添加日志条目失败: {e}")
    
    def _update_log_display(self, formatted_message: str, level_tag: str):
        """更新日志显示（主线程中执行）"""
        try:
            self.log_text.config(state=tk.NORMAL)
            
            # 插入日志消息
            self.log_text.insert(tk.END, formatted_message, level_tag)
            
            # 自动滚动到底部
            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
            
            self.log_text.config(state=tk.DISABLED)
            
            # 更新日志计数
            self._update_log_count()
            
        except Exception as e:
            print(f"更新日志显示失败: {e}")
    
    def _should_show_level(self, level: str) -> bool:
        """检查是否应该显示指定级别的日志"""
        level_vars = {
            'DEBUG': self.show_debug_var,
            'INFO': self.show_info_var,
            'WARNING': self.show_warning_var,
            'ERROR': self.show_error_var,
            'CRITICAL': self.show_error_var  # CRITICAL归类为ERROR
        }
        
        var = level_vars.get(level.upper())
        return var.get() if var else True
    
    def _clear_logs(self):
        """清空日志"""
        try:
            if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
                self.log_text.config(state=tk.NORMAL)
                self.log_text.delete(1.0, tk.END)
                self.log_text.config(state=tk.DISABLED)
                
                # 清空缓存
                self.log_buffer.clear()
                
                # 清空搜索结果
                self.search_results.clear()
                self.current_search_index = 0
                
                # 更新计数
                self._update_log_count()
                
                # 添加清空日志的记录
                self._add_system_log("日志已清空")
                
        except Exception as e:
            messagebox.showerror("错误", f"清空日志失败: {e}")
    
    def _export_logs(self):
        """导出日志"""
        try:
            if not self.log_buffer:
                messagebox.showwarning("警告", "没有日志可以导出")
                return
            
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                title="导出日志",
                defaultextension=".log",
                filetypes=[
                    ("日志文件", "*.log"),
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ]
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# HubStudio 日志导出\n")
                    f.write(f"# 导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 日志条数: {len(self.log_buffer)}\n\n")
                    
                    for log_entry in self.log_buffer:
                        f.write(log_entry['formatted'])
                
                messagebox.showinfo("完成", f"日志已导出到: {file_path}")
                self._add_system_log(f"日志已导出到: {file_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")
    
    def _toggle_pause(self):
        """切换暂停/恢复状态"""
        try:
            self.paused = not self.paused
            
            if self.paused:
                self.pause_btn.config(text="恢复")
                self._update_status("日志已暂停")
            else:
                self.pause_btn.config(text="暂停")
                self._update_status("日志已恢复")
                
        except Exception as e:
            print(f"切换暂停状态失败: {e}")
    
    def _apply_filters(self):
        """应用日志级别过滤"""
        try:
            # 重新构建日志显示
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            
            # 重新添加符合过滤条件的日志
            for log_entry in self.log_buffer:
                if self._should_show_level(log_entry['level']):
                    self.log_text.insert(tk.END, log_entry['formatted'], 
                                        log_entry['level'].lower())
            
            # 自动滚动到底部
            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
            
            self.log_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"应用过滤器失败: {e}")
    
    def _search_logs(self, event=None):
        """搜索日志"""
        try:
            search_text = self.search_entry.get().strip()
            if not search_text:
                return
            
            # 清除之前的搜索高亮
            self.log_text.tag_remove("search_highlight", "1.0", tk.END)
            self.log_text.tag_remove("current_search", "1.0", tk.END)
            
            # 搜索文本
            self.search_results.clear()
            start_pos = "1.0"
            
            while True:
                pos = self.log_text.search(search_text, start_pos, tk.END)
                if not pos:
                    break
                
                end_pos = f"{pos}+{len(search_text)}c"
                self.search_results.append((pos, end_pos))
                start_pos = end_pos
            
            if self.search_results:
                # 高亮所有搜索结果
                for start_pos, end_pos in self.search_results:
                    self.log_text.tag_add("search_highlight", start_pos, end_pos)
                
                # 跳转到第一个结果
                self.current_search_index = 0
                self._highlight_current_search()
                
                self._update_status(f"找到 {len(self.search_results)} 个匹配项")
            else:
                self._update_status("未找到匹配项")
                
        except Exception as e:
            print(f"搜索日志失败: {e}")
    
    def _search_next(self):
        """搜索下一个"""
        try:
            if not self.search_results:
                return
            
            self.current_search_index = (self.current_search_index + 1) % len(self.search_results)
            self._highlight_current_search()
            
        except Exception as e:
            print(f"搜索下一个失败: {e}")
    
    def _search_prev(self):
        """搜索上一个"""
        try:
            if not self.search_results:
                return
            
            self.current_search_index = (self.current_search_index - 1) % len(self.search_results)
            self._highlight_current_search()
            
        except Exception as e:
            print(f"搜索上一个失败: {e}")
    
    def _highlight_current_search(self):
        """高亮当前搜索结果"""
        try:
            if not self.search_results:
                return
            
            # 清除当前高亮
            self.log_text.tag_remove("current_search", "1.0", tk.END)
            
            # 高亮当前结果
            start_pos, end_pos = self.search_results[self.current_search_index]
            self.log_text.tag_add("current_search", start_pos, end_pos)
            
            # 滚动到当前结果
            self.log_text.see(start_pos)
            
            # 更新状态
            self._update_status(f"第 {self.current_search_index + 1} 个，共 {len(self.search_results)} 个")
            
        except Exception as e:
            print(f"高亮当前搜索结果失败: {e}")
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            context_menu = tk.Menu(self.parent, tearoff=0)
            
            context_menu.add_command(label="复制", command=self._copy_selected)
            context_menu.add_command(label="全选", command=self._select_all)
            context_menu.add_separator()
            context_menu.add_command(label="清空日志", command=self._clear_logs)
            context_menu.add_command(label="导出日志", command=self._export_logs)
            
            context_menu.post(event.x_root, event.y_root)
            
        except Exception as e:
            print(f"显示右键菜单失败: {e}")
    
    def _copy_selected(self):
        """复制选中的文本"""
        try:
            if self.log_text.tag_ranges(tk.SEL):
                selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
                self.parent.clipboard_clear()
                self.parent.clipboard_append(selected_text)
        except Exception as e:
            print(f"复制文本失败: {e}")
    
    def _select_all(self):
        """全选文本"""
        try:
            self.log_text.tag_add(tk.SEL, "1.0", tk.END)
        except Exception as e:
            print(f"全选文本失败: {e}")
    
    def _update_log_count(self):
        """更新日志计数显示"""
        try:
            count = len(self.log_buffer)
            self.log_count_label.config(text=f"日志: {count} 条")
        except Exception as e:
            print(f"更新日志计数失败: {e}")
    
    def _add_system_log(self, message: str):
        """添加系统日志"""
        try:
            log_entry = {
                'level': 'INFO',
                'message': message,
                'timestamp': datetime.datetime.now()
            }
            self.log_queue.put(log_entry)
        except Exception as e:
            print(f"添加系统日志失败: {e}")
    
    # ==================== 事件处理方法 ====================
    
    def _on_log_message(self, log_data):
        """处理日志消息事件"""
        try:
            self.log_queue.put(log_data)
        except Exception as e:
            print(f"处理日志消息事件失败: {e}")
    
    def _on_status_update(self, status_data):
        """处理状态更新事件"""
        try:
            status = status_data.get('status', '')
            self._update_status(status)
        except Exception as e:
            print(f"处理状态更新事件失败: {e}")
    
    def _on_progress_update(self, progress_data):
        """处理进度更新事件"""
        try:
            progress = progress_data.get('progress', 0)
            total = progress_data.get('total', 100)
            
            if total > 0:
                percentage = (progress / total) * 100
                self.progress_bar.config(mode='determinate', value=percentage)
            else:
                self.progress_bar.config(mode='indeterminate')
                
        except Exception as e:
            print(f"处理进度更新事件失败: {e}")
    
    def _on_upload_started(self, upload_data):
        """处理上传开始事件"""
        try:
            self.progress_bar.start()
            self._update_status("上传开始...")
            self._add_system_log("开始上传视频文件")
        except Exception as e:
            print(f"处理上传开始事件失败: {e}")
    
    def _on_upload_completed(self, upload_data):
        """处理上传完成事件"""
        try:
            self.progress_bar.stop()
            self.progress_bar.config(value=100)
            self._update_status("上传完成")
            
            success_count = upload_data.get('success_count', 0)
            total_count = upload_data.get('total_count', 0)
            self._add_system_log(f"上传完成: {success_count}/{total_count} 个文件成功")
        except Exception as e:
            print(f"处理上传完成事件失败: {e}")
    
    def _on_upload_failed(self, upload_data):
        """处理上传失败事件"""
        try:
            self.progress_bar.stop()
            self.progress_bar.config(value=0)
            self._update_status("上传失败")
            
            error_message = upload_data.get('error', '未知错误')
            self._add_system_log(f"上传失败: {error_message}")
        except Exception as e:
            print(f"处理上传失败事件失败: {e}")
    
    # ==================== 公共接口 ====================
    
    def add_log(self, level: str, message: str):
        """添加日志（公共接口）"""
        try:
            log_entry = {
                'level': level.upper(),
                'message': message,
                'timestamp': datetime.datetime.now()
            }
            self.log_queue.put(log_entry)
        except Exception as e:
            print(f"添加日志失败: {e}")
    
    def _update_status(self, status: str):
        """更新状态显示"""
        try:
            self.status_label.config(text=status)
        except Exception as e:
            print(f"更新状态显示失败: {e}")
    
    def set_progress(self, progress: int, total: int = 100):
        """设置进度（公共接口）"""
        try:
            if total > 0:
                percentage = (progress / total) * 100
                self.progress_bar.config(mode='determinate', value=percentage)
            else:
                self.progress_bar.config(mode='indeterminate')
        except Exception as e:
            print(f"设置进度失败: {e}")
    
    def start_progress(self):
        """开始进度动画"""
        try:
            self.progress_bar.start()
        except Exception as e:
            print(f"开始进度动画失败: {e}")
    
    def stop_progress(self):
        """停止进度动画"""
        try:
            self.progress_bar.stop()
        except Exception as e:
            print(f"停止进度动画失败: {e}")
    
    def clear_logs(self):
        """清空日志（公共接口）"""
        self._clear_logs()
    
    def export_logs(self, file_path: str = None):
        """导出日志（公共接口）"""
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# HubStudio 日志导出\n")
                    f.write(f"# 导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 日志条数: {len(self.log_buffer)}\n\n")
                    
                    for log_entry in self.log_buffer:
                        f.write(log_entry['formatted'])
                
                return True
            except Exception as e:
                print(f"导出日志失败: {e}")
                return False
        else:
            self._export_logs()
    
    def get_log_count(self) -> int:
        """获取日志条数"""
        return len(self.log_buffer)
    
    def get_logs(self) -> List[Dict[str, Any]]:
        """获取所有日志"""
        return self.log_buffer.copy()
    
    def stop_log_processor(self):
        """停止日志处理器"""
        self.log_processor_running = False