# HubStudio 视频上传工具重构方案文档

## 1. 项目现状分析

### 1.1 架构问题分析

#### 当前架构问题
- **分层架构不一致**：声称采用分层架构，但实际实现中层次边界模糊
- **组件耦合度高**：GUI层直接调用API层，缺少业务逻辑层的中介
- **依赖注入不完整**：组件间依赖关系硬编码，难以测试和维护
- **模块职责不清**：存在功能重复的模块（main.py vs start_gui.py）

#### 代码质量问题
- **错误处理不统一**：异常处理策略不一致，缺少统一的错误分类
- **并发处理复杂**：多个并发管理器职责重叠，存在线程安全问题
- **资源管理不完善**：可能导致内存泄漏和资源竞争

#### 技术债务
- **依赖管理混乱**：requirements.txt包含过时依赖，缺少版本锁定
- **配置系统问题**：配置格式不统一，缺少验证机制
- **安全问题**：敏感信息明文存储

### 1.2 功能缺陷分析

#### GUI界面问题
- **用户体验差**：界面布局不直观，缺少必要的用户反馈
- **功能不完整**：缺少批量文件选择、并发控制等关键功能
- **错误恢复机制不完善**：上传失败后的处理不够友好

#### 业务逻辑问题
- **上传流程不够健壮**：缺少断点续传、智能重试等功能
- **并发控制不精确**：无法精确控制并发上传数量
- **状态管理混乱**：上传状态跟踪不准确

## 2. 重构目标

### 2.1 核心目标
1. **提升用户体验**：创建直观、友好的GUI界面
2. **增强系统稳定性**：完善错误处理和恢复机制
3. **优化性能**：实现高效的并发上传管理
4. **提高可维护性**：采用清晰的架构设计和代码规范

### 2.2 功能目标
1. **GUI界面设计**：
   - 用户友好的图形界面
   - 视频文件选择功能（支持常见格式）
   - 多目标浏览器环境配置
   - 并发上传数量控制

2. **浏览器环境管理**：
   - 支持多个浏览器环境同时操作
   - 可配置的并发上传数量限制
   - 浏览器实例生命周期管理

3. **自动化上传功能**：
   - 视频文件自动上传流程
   - 批量处理多个视频
   - 上传进度显示和状态反馈
   - 错误处理和重试机制

## 3. 技术栈选择

### 3.1 推荐技术栈

#### 后端框架
- **Python 3.9+**：保持现有技术栈，确保兼容性
- **FastAPI**：替代直接的API调用，提供更好的API管理
- **SQLAlchemy + SQLite**：数据持久化，替代配置文件存储
- **Celery + Redis**：异步任务处理，优化并发管理

#### 前端框架
- **Tkinter + CustomTkinter**：现代化的GUI组件库
- **或 PyQt6/PySide6**：更强大的GUI框架（可选）
- **或 Web界面（Flask/FastAPI + React）**：跨平台解决方案（可选）

#### 工具库
- **Pydantic**：数据验证和序列化
- **Loguru**：统一的日志管理
- **Click**：命令行接口
- **Pytest**：单元测试框架

### 3.2 架构设计

#### 分层架构
```
┌─────────────────────────────────────┐
│           Presentation Layer        │  GUI界面层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   GUI App   │ │   Web Interface │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           Application Layer         │  应用服务层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │Upload Service│ │Environment Mgmt │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│            Domain Layer             │  领域模型层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │Video Entity │ │ Upload Session  │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         Infrastructure Layer        │  基础设施层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │  Database   │ │   External APIs │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
```

## 4. 详细重构方案

### 4.1 第一阶段：基础设施重构（预计2周）

#### 4.1.1 项目结构重组
```
hubstudio_uploader/
├── src/                          # 源代码目录
│   ├── domain/                   # 领域模型
│   │   ├── entities/            # 实体类
│   │   ├── value_objects/       # 值对象
│   │   └── repositories/        # 仓储接口
│   ├── application/             # 应用服务
│   │   ├── services/           # 应用服务
│   │   ├── dto/                # 数据传输对象
│   │   └── interfaces/         # 接口定义
│   ├── infrastructure/          # 基础设施
│   │   ├── database/           # 数据库相关
│   │   ├── external_apis/      # 外部API
│   │   ├── logging/            # 日志系统
│   │   └── config/             # 配置管理
│   └── presentation/            # 表现层
│       ├── gui/                # GUI界面
│       ├── cli/                # 命令行界面
│       └── web/                # Web界面（可选）
├── tests/                       # 测试代码
├── docs/                        # 文档
├── scripts/                     # 脚本文件
└── requirements/                # 依赖管理
    ├── base.txt
    ├── dev.txt
    └── prod.txt
```

#### 4.1.2 依赖管理优化
- 清理无用依赖
- 添加版本锁定
- 分离开发和生产依赖
- 添加安全扫描

#### 4.1.3 配置系统重构
- 统一使用YAML格式
- 添加配置验证
- 实现环境变量支持
- 敏感信息加密存储

### 4.2 第二阶段：核心业务重构（预计3周）

#### 4.2.1 领域模型设计
```python
# 视频实体
class Video:
    def __init__(self, file_path: Path, title: str, description: str):
        self.file_path = file_path
        self.title = title
        self.description = description
        self.tags = []
        self.privacy = PrivacyLevel.PRIVATE
        self.status = VideoStatus.PENDING

# 上传会话
class UploadSession:
    def __init__(self, videos: List[Video], config: UploadConfig):
        self.session_id = uuid.uuid4()
        self.videos = videos
        self.config = config
        self.status = SessionStatus.CREATED
        self.progress = 0.0
```

#### 4.2.2 应用服务设计
```python
class UploadService:
    def __init__(self, 
                 video_repository: VideoRepository,
                 browser_service: BrowserService,
                 notification_service: NotificationService):
        self.video_repository = video_repository
        self.browser_service = browser_service
        self.notification_service = notification_service
    
    async def upload_videos(self, session: UploadSession) -> UploadResult:
        # 实现上传逻辑
        pass
```

### 4.3 第三阶段：GUI界面重构（预计2周）

#### 4.3.1 主界面设计
- 采用现代化的Material Design风格
- 响应式布局，支持窗口缩放
- 暗色/亮色主题切换

#### 4.3.2 核心功能界面
1. **文件选择面板**
   - 拖拽上传支持
   - 批量文件选择
   - 文件预览和信息显示

2. **配置管理面板**
   - 浏览器环境配置
   - 上传参数设置
   - API连接管理

3. **上传控制面板**
   - 并发数量控制
   - 上传队列管理
   - 实时进度显示

4. **日志监控面板**
   - 分级日志显示
   - 实时状态更新
   - 错误信息高亮

### 4.4 第四阶段：高级功能实现（预计2周）

#### 4.4.1 并发控制优化
- 智能并发数量调整
- 资源池管理
- 负载均衡

#### 4.4.2 错误处理和恢复
- 断点续传
- 智能重试机制
- 错误分类和处理

#### 4.4.3 性能优化
- 内存使用优化
- 网络请求优化
- 缓存机制

## 5. 实施计划

### 5.1 时间安排
- **第1-2周**：基础设施重构
- **第3-5周**：核心业务重构
- **第6-7周**：GUI界面重构
- **第8-9周**：高级功能实现
- **第10周**：测试和优化

### 5.2 里程碑
1. **里程碑1**：完成项目结构重组和依赖管理
2. **里程碑2**：完成核心业务逻辑重构
3. **里程碑3**：完成GUI界面重构
4. **里程碑4**：完成所有功能并通过测试

### 5.3 风险评估
- **技术风险**：新架构的学习成本
- **时间风险**：重构工作量可能超出预期
- **兼容性风险**：现有配置和数据的迁移

## 6. 质量保证

### 6.1 代码质量
- 代码审查流程
- 自动化测试覆盖率 > 80%
- 静态代码分析
- 性能基准测试

### 6.2 文档要求
- API文档
- 用户手册
- 开发者指南
- 部署文档

## 7. 实现示例

### 7.1 核心组件示例

#### 视频实体类
```python
from dataclasses import dataclass
from pathlib import Path
from enum import Enum
from typing import List, Optional
import uuid

class VideoStatus(Enum):
    PENDING = "pending"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    FAILED = "failed"

class PrivacyLevel(Enum):
    PUBLIC = "public"
    UNLISTED = "unlisted"
    PRIVATE = "private"

@dataclass
class Video:
    file_path: Path
    title: str
    description: str
    tags: List[str]
    privacy: PrivacyLevel
    category: str
    status: VideoStatus = VideoStatus.PENDING
    upload_progress: float = 0.0
    error_message: Optional[str] = None

    @property
    def file_size(self) -> int:
        return self.file_path.stat().st_size if self.file_path.exists() else 0

    @property
    def file_name(self) -> str:
        return self.file_path.name
```

#### 上传服务类
```python
import asyncio
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

class UploadService:
    def __init__(self,
                 browser_manager: BrowserManager,
                 config: UploadConfig,
                 logger: Logger):
        self.browser_manager = browser_manager
        self.config = config
        self.logger = logger
        self.executor = ThreadPoolExecutor(max_workers=config.max_concurrent)

    async def upload_videos_batch(self, videos: List[Video]) -> Dict[str, Any]:
        """批量上传视频"""
        session_id = str(uuid.uuid4())
        results = {
            'session_id': session_id,
            'total_videos': len(videos),
            'completed': 0,
            'failed': 0,
            'results': []
        }

        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent)

        # 创建上传任务
        tasks = [
            self._upload_single_video(video, semaphore, session_id)
            for video in videos
        ]

        # 等待所有任务完成
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        for result in completed_results:
            if isinstance(result, Exception):
                results['failed'] += 1
                results['results'].append({
                    'status': 'error',
                    'error': str(result)
                })
            else:
                if result['success']:
                    results['completed'] += 1
                else:
                    results['failed'] += 1
                results['results'].append(result)

        return results

    async def _upload_single_video(self, video: Video, semaphore: asyncio.Semaphore, session_id: str) -> Dict[str, Any]:
        """上传单个视频"""
        async with semaphore:
            try:
                # 获取浏览器实例
                browser = await self.browser_manager.get_available_browser()

                # 执行上传
                result = await self._perform_upload(video, browser, session_id)

                # 释放浏览器实例
                await self.browser_manager.release_browser(browser)

                return result

            except Exception as e:
                self.logger.error(f"上传视频失败: {video.file_name}, 错误: {e}")
                return {
                    'video_path': str(video.file_path),
                    'success': False,
                    'error': str(e)
                }
```

### 7.2 GUI组件示例

#### 现代化文件选择组件
```python
import customtkinter as ctk
from tkinter import filedialog
from typing import List, Callable

class VideoFileSelector(ctk.CTkFrame):
    def __init__(self, parent, on_files_selected: Callable[[List[str]], None]):
        super().__init__(parent)
        self.on_files_selected = on_files_selected
        self.selected_files = []

        self._create_widgets()

    def _create_widgets(self):
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="视频文件选择",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 20))

        # 按钮框架
        button_frame = ctk.CTkFrame(self)
        button_frame.pack(fill="x", padx=20, pady=10)

        # 选择文件按钮
        select_files_btn = ctk.CTkButton(
            button_frame,
            text="选择视频文件",
            command=self._select_files,
            width=150
        )
        select_files_btn.pack(side="left", padx=10)

        # 选择文件夹按钮
        select_folder_btn = ctk.CTkButton(
            button_frame,
            text="选择文件夹",
            command=self._select_folder,
            width=150
        )
        select_folder_btn.pack(side="left", padx=10)

        # 清空按钮
        clear_btn = ctk.CTkButton(
            button_frame,
            text="清空列表",
            command=self._clear_files,
            width=100,
            fg_color="red",
            hover_color="darkred"
        )
        clear_btn.pack(side="right", padx=10)

        # 文件列表
        self.file_listbox = ctk.CTkScrollableFrame(self)
        self.file_listbox.pack(fill="both", expand=True, padx=20, pady=10)

        # 统计信息
        self.stats_label = ctk.CTkLabel(
            self,
            text="已选择 0 个文件",
            font=ctk.CTkFont(size=12)
        )
        self.stats_label.pack(pady=10)

    def _select_files(self):
        """选择视频文件"""
        filetypes = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择视频文件",
            filetypes=filetypes
        )

        if files:
            self._add_files(list(files))

    def _select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含视频的文件夹")

        if folder:
            # 扫描文件夹中的视频文件
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
            video_files = []

            for file_path in Path(folder).rglob('*'):
                if file_path.suffix.lower() in video_extensions:
                    video_files.append(str(file_path))

            if video_files:
                self._add_files(video_files)
            else:
                ctk.CTkMessagebox(
                    title="提示",
                    message="所选文件夹中没有找到视频文件",
                    icon="warning"
                ).show()

    def _add_files(self, files: List[str]):
        """添加文件到列表"""
        new_files = [f for f in files if f not in self.selected_files]
        self.selected_files.extend(new_files)

        # 更新界面
        self._update_file_list()
        self._update_stats()

        # 通知回调
        if self.on_files_selected:
            self.on_files_selected(self.selected_files)

    def _update_file_list(self):
        """更新文件列表显示"""
        # 清空现有显示
        for widget in self.file_listbox.winfo_children():
            widget.destroy()

        # 添加文件项
        for i, file_path in enumerate(self.selected_files):
            file_frame = ctk.CTkFrame(self.file_listbox)
            file_frame.pack(fill="x", pady=2)

            # 文件名
            file_name = Path(file_path).name
            name_label = ctk.CTkLabel(
                file_frame,
                text=file_name,
                anchor="w"
            )
            name_label.pack(side="left", fill="x", expand=True, padx=10)

            # 删除按钮
            remove_btn = ctk.CTkButton(
                file_frame,
                text="移除",
                width=60,
                height=25,
                command=lambda idx=i: self._remove_file(idx)
            )
            remove_btn.pack(side="right", padx=10)

    def _remove_file(self, index: int):
        """移除指定文件"""
        if 0 <= index < len(self.selected_files):
            self.selected_files.pop(index)
            self._update_file_list()
            self._update_stats()

            if self.on_files_selected:
                self.on_files_selected(self.selected_files)

    def _clear_files(self):
        """清空所有文件"""
        self.selected_files.clear()
        self._update_file_list()
        self._update_stats()

        if self.on_files_selected:
            self.on_files_selected(self.selected_files)

    def _update_stats(self):
        """更新统计信息"""
        count = len(self.selected_files)
        total_size = sum(Path(f).stat().st_size for f in self.selected_files if Path(f).exists())
        size_mb = total_size / (1024 * 1024)

        self.stats_label.configure(
            text=f"已选择 {count} 个文件，总大小: {size_mb:.1f} MB"
        )
```

## 8. 后续维护

### 8.1 监控和日志
- 应用性能监控
- 错误追踪
- 用户行为分析

### 8.2 持续改进
- 用户反馈收集
- 功能迭代计划
- 技术债务管理
