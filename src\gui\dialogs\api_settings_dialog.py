"""
API设置对话框 - HubStudio API配置界面
"""

import tkinter as tk
import customtkinter as ctk
from typing import Dict, Any, Optional, Callable
import threading
import time

from ..theme_manager import theme_manager
from ...core.logger import get_logger


class APISettingsDialog(ctk.CTkToplevel):
    """API设置对话框"""
    
    def __init__(self, parent, current_config: Dict[str, Any] = None, 
                 on_save_callback: Callable = None, test_connection_callback: Callable = None):
        super().__init__(parent)
        
        self.logger = get_logger("APISettingsDialog")
        self.on_save_callback = on_save_callback
        self.test_connection_callback = test_connection_callback
        
        # 当前配置
        self.current_config = current_config or {}
        
        # 测试状态
        self.testing_connection = False
        
        self._setup_window()
        self._setup_ui()
        self._load_current_config()
        
        # 设置为模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self._center_window()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.title("🔧 HubStudio API 设置")
        self.geometry("500x600")
        self.resizable(False, False)
        
        # 应用主题
        self.configure(fg_color=theme_manager.get_color('background'))
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self, **theme_manager.create_frame_style('transparent'))
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="🔧 HubStudio API 配置",
            **theme_manager.create_label_style('heading')
        )
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        info_label = ctk.CTkLabel(
            main_frame,
            text="请输入HubStudio浏览器的API连接信息\n确保HubStudio浏览器已启动并开启API服务",
            **theme_manager.create_label_style('secondary'),
            justify="center"
        )
        info_label.pack(pady=(0, 30))
        
        # 配置表单
        self._create_config_form(main_frame)
        
        # 测试连接区域
        self._create_test_section(main_frame)
        
        # 按钮区域
        self._create_button_section(main_frame)
    
    def _create_config_form(self, parent):
        """创建配置表单"""
        # 表单容器
        form_frame = ctk.CTkFrame(parent, **theme_manager.create_frame_style('card'))
        form_frame.pack(fill="x", pady=(0, 20))
        
        # 服务器URL
        url_label = ctk.CTkLabel(
            form_frame,
            text="🌐 服务器地址 (Server URL):",
            **theme_manager.create_label_style('default')
        )
        url_label.pack(anchor="w", padx=20, pady=(20, 5))
        
        self.url_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="http://127.0.0.1:50325",
            **theme_manager.create_entry_style()
        )
        self.url_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # API ID
        api_id_label = ctk.CTkLabel(
            form_frame,
            text="🔑 API ID:",
            **theme_manager.create_label_style('default')
        )
        api_id_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.api_id_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入API ID",
            **theme_manager.create_entry_style()
        )
        self.api_id_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # API Secret
        api_secret_label = ctk.CTkLabel(
            form_frame,
            text="🔐 API Secret:",
            **theme_manager.create_label_style('default')
        )
        api_secret_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.api_secret_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入API Secret",
            show="*",  # 隐藏密码
            **theme_manager.create_entry_style()
        )
        self.api_secret_entry.pack(fill="x", padx=20, pady=(0, 20))
    
    def _create_test_section(self, parent):
        """创建测试连接区域"""
        test_frame = ctk.CTkFrame(parent, **theme_manager.create_frame_style('card'))
        test_frame.pack(fill="x", pady=(0, 20))
        
        # 测试按钮
        self.test_btn = ctk.CTkButton(
            test_frame,
            text="🔍 测试连接",
            command=self._test_connection,
            **theme_manager.create_button_style('info')
        )
        self.test_btn.pack(pady=20)
        
        # 测试结果显示
        self.test_result_label = ctk.CTkLabel(
            test_frame,
            text="",
            **theme_manager.create_label_style('small')
        )
        self.test_result_label.pack(pady=(0, 20))
        
        # 环境数量显示
        self.env_count_label = ctk.CTkLabel(
            test_frame,
            text="",
            **theme_manager.create_label_style('default')
        )
        self.env_count_label.pack(pady=(0, 20))
    
    def _create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(fill="x", pady=(10, 0))
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ 取消",
            command=self._on_cancel,
            **theme_manager.create_button_style('secondary')
        )
        cancel_btn.pack(side="left", padx=(0, 10))
        
        # 保存按钮
        self.save_btn = ctk.CTkButton(
            button_frame,
            text="💾 保存配置",
            command=self._on_save,
            **theme_manager.create_button_style('success')
        )
        self.save_btn.pack(side="right")
    
    def _load_current_config(self):
        """加载当前配置"""
        if self.current_config:
            self.url_entry.insert(0, self.current_config.get('server_url', 'http://127.0.0.1:50325'))
            self.api_id_entry.insert(0, self.current_config.get('api_id', ''))
            self.api_secret_entry.insert(0, self.current_config.get('api_secret', ''))
    
    def _test_connection(self):
        """测试API连接"""
        if self.testing_connection:
            return
        
        # 获取输入值
        server_url = self.url_entry.get().strip()
        api_id = self.api_id_entry.get().strip()
        api_secret = self.api_secret_entry.get().strip()
        
        # 验证输入
        if not server_url:
            self._show_test_result("❌ 请输入服务器地址", "error")
            return
        
        if not api_id:
            self._show_test_result("❌ 请输入API ID", "error")
            return
        
        if not api_secret:
            self._show_test_result("❌ 请输入API Secret", "error")
            return
        
        # 开始测试
        self.testing_connection = True
        self.test_btn.configure(text="🔄 测试中...", state="disabled")
        self._show_test_result("🔄 正在测试连接...", "info")
        
        # 在后台线程中测试连接
        def test_thread():
            try:
                if self.test_connection_callback:
                    result = self.test_connection_callback(server_url, api_id, api_secret)
                    
                    # 在主线程中更新UI
                    self.after(0, lambda: self._handle_test_result(result))
                else:
                    self.after(0, lambda: self._handle_test_result({
                        'success': False,
                        'message': '测试功能不可用'
                    }))
            except Exception as e:
                self.after(0, lambda: self._handle_test_result({
                    'success': False,
                    'message': f'测试异常: {str(e)}'
                }))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def _handle_test_result(self, result: Dict[str, Any]):
        """处理测试结果"""
        self.testing_connection = False
        self.test_btn.configure(text="🔍 测试连接", state="normal")
        
        if result.get('success', False):
            message = "✅ 连接成功！"
            status = "success"
            
            # 显示环境数量
            env_count = result.get('environment_count', 0)
            if env_count > 0:
                self.env_count_label.configure(text=f"🌐 发现 {env_count} 个浏览器环境")
            else:
                self.env_count_label.configure(text="⚠️ 未发现可用的浏览器环境")
        else:
            message = f"❌ 连接失败: {result.get('message', '未知错误')}"
            status = "error"
            self.env_count_label.configure(text="")
        
        self._show_test_result(message, status)
    
    def _show_test_result(self, message: str, status: str):
        """显示测试结果"""
        self.test_result_label.configure(text=message)
        
        # 根据状态设置颜色
        if status == "success":
            color = theme_manager.get_status_color('success')
        elif status == "error":
            color = theme_manager.get_status_color('error')
        else:
            color = theme_manager.get_status_color('info')
        
        # 注意：这里我们只改变指示器颜色，文字保持白色
        # 可以考虑添加一个状态指示器而不是改变文字颜色
    
    def _on_save(self):
        """保存配置"""
        # 获取输入值
        config = {
            'server_url': self.url_entry.get().strip(),
            'api_id': self.api_id_entry.get().strip(),
            'api_secret': self.api_secret_entry.get().strip()
        }
        
        # 验证输入
        if not config['server_url']:
            self._show_test_result("❌ 请输入服务器地址", "error")
            return
        
        if not config['api_id']:
            self._show_test_result("❌ 请输入API ID", "error")
            return
        
        if not config['api_secret']:
            self._show_test_result("❌ 请输入API Secret", "error")
            return
        
        # 调用保存回调
        if self.on_save_callback:
            try:
                success = self.on_save_callback(config)
                if success:
                    self.logger.info("API配置保存成功")
                    self.destroy()
                else:
                    self._show_test_result("❌ 保存配置失败", "error")
            except Exception as e:
                self.logger.error(f"保存API配置异常: {e}")
                self._show_test_result(f"❌ 保存失败: {str(e)}", "error")
        else:
            self.destroy()
    
    def _on_cancel(self):
        """取消操作"""
        self.destroy()
    
    def _center_window(self):
        """居中显示窗口"""
        self.update_idletasks()
        
        # 获取窗口尺寸
        window_width = self.winfo_width()
        window_height = self.winfo_height()
        
        # 获取屏幕尺寸
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
