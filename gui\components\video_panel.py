"""
视频面板组件 - 负责视频文件选择和信息配置
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import List, Dict, Any, Optional


class VideoPanel:
    """视频面板组件"""
    
    def __init__(self, parent, event_manager, file_manager):
        self.parent = parent
        self.event_manager = event_manager
        self.file_manager = file_manager
        
        # 界面变量
        self.selected_files = []
        self.video_info = {
            'title_template': '自动上传视频 - {filename}',
            'description_template': '通过HubStudio自动上传的视频',
            'tags': ['自动上传', 'HubStudio'],
            'privacy': 'private',
            'category': '22'
        }
        
        # 界面控件引用
        self.file_listbox = None
        self.title_entry = None
        self.description_text = None
        self.tags_entry = None
        self.privacy_combo = None
        self.category_combo = None
        
        # 统计标签
        self.file_count_label = None
        self.total_size_label = None
        
        self.create_widgets()
        self.setup_events()
        self.update_file_stats()
    
    def create_widgets(self):
        """创建视频面板界面"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="视频文件", padding="15")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文件选择区域
        self._create_file_selection_section()
        
        # 创建视频信息配置区域
        self._create_video_info_section()
    
    def _create_file_selection_section(self):
        """创建文件选择区域"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 按钮区域
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 选择文件按钮
        select_btn = ttk.Button(
            button_frame,
            text="选择视频文件",
            command=self._select_files
        )
        select_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 选择文件夹按钮
        select_folder_btn = ttk.Button(
            button_frame,
            text="选择文件夹",
            command=self._select_folder
        )
        select_folder_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清空按钮
        clear_btn = ttk.Button(
            button_frame,
            text="清空列表",
            command=self._clear_files
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 移除选中按钮
        remove_btn = ttk.Button(
            button_frame,
            text="移除选中",
            command=self._remove_selected
        )
        remove_btn.pack(side=tk.LEFT)
        
        # 统计信息
        stats_frame = ttk.Frame(button_frame)
        stats_frame.pack(side=tk.RIGHT)
        
        self.file_count_label = ttk.Label(stats_frame, text="文件数: 0", font=("Arial", 9))
        self.file_count_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.total_size_label = ttk.Label(stats_frame, text="总大小: 0 MB", font=("Arial", 9))
        self.total_size_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 文件列表区域
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview来显示文件列表
        columns = ('文件名', '大小', '路径')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        self.file_tree.heading('文件名', text='文件名')
        self.file_tree.heading('大小', text='大小')
        self.file_tree.heading('路径', text='完整路径')
        
        # 设置列宽
        self.file_tree.column('文件名', width=200)
        self.file_tree.column('大小', width=80)
        self.file_tree.column('路径', width=300)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定双击事件
        self.file_tree.bind('<Double-1>', self._on_file_double_click)
    
    def _create_video_info_section(self):
        """创建视频信息配置区域"""
        # 视频信息框架
        info_frame = ttk.LabelFrame(self.main_frame, text="视频信息模板", padding="10")
        info_frame.pack(fill=tk.X)
        
        # 创建Notebook来组织不同的配置页面
        notebook = ttk.Notebook(info_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 基本信息页面
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="基本信息")
        self._create_basic_info_tab(basic_frame)
        
        # 高级设置页面
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="高级设置")
        self._create_advanced_info_tab(advanced_frame)
    
    def _create_basic_info_tab(self, parent):
        """创建基本信息标签页"""
        # 标题模板
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(title_frame, text="标题模板:").pack(anchor=tk.W)
        self.title_entry = ttk.Entry(title_frame, width=50)
        self.title_entry.pack(fill=tk.X, pady=(2, 0))
        self.title_entry.insert(0, self.video_info['title_template'])
        
        # 提示标签
        hint_label = ttk.Label(title_frame, text="提示: 使用 {filename} 作为文件名占位符", 
                              font=("Arial", 8), foreground="gray")
        hint_label.pack(anchor=tk.W, pady=(2, 0))
        
        # 描述模板
        desc_frame = ttk.Frame(parent)
        desc_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 5))
        
        ttk.Label(desc_frame, text="描述模板:").pack(anchor=tk.W)
        
        # 描述文本框
        desc_text_frame = ttk.Frame(desc_frame)
        desc_text_frame.pack(fill=tk.BOTH, expand=True, pady=(2, 0))
        
        self.description_text = tk.Text(desc_text_frame, height=4, wrap=tk.WORD)
        desc_scrollbar = ttk.Scrollbar(desc_text_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        
        self.description_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        desc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.description_text.insert(tk.END, self.video_info['description_template'])
        
        # 标签
        tags_frame = ttk.Frame(parent)
        tags_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(tags_frame, text="标签 (用逗号分隔):").pack(anchor=tk.W)
        self.tags_entry = ttk.Entry(tags_frame, width=50)
        self.tags_entry.pack(fill=tk.X, pady=(2, 0))
        self.tags_entry.insert(0, ', '.join(self.video_info['tags']))
    
    def _create_advanced_info_tab(self, parent):
        """创建高级设置标签页"""
        # 隐私设置
        privacy_frame = ttk.Frame(parent)
        privacy_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(privacy_frame, text="隐私设置:").pack(side=tk.LEFT)
        self.privacy_combo = ttk.Combobox(privacy_frame, width=15, state="readonly")
        self.privacy_combo['values'] = ('private', 'unlisted', 'public')
        self.privacy_combo.set(self.video_info['privacy'])
        self.privacy_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 分类设置
        category_frame = ttk.Frame(parent)
        category_frame.pack(fill=tk.X, pady=(10, 5))
        
        ttk.Label(category_frame, text="视频分类:").pack(side=tk.LEFT)
        self.category_combo = ttk.Combobox(category_frame, width=20, state="readonly")
        self.category_combo['values'] = (
            '1 - Film & Animation',
            '2 - Autos & Vehicles', 
            '10 - Music',
            '15 - Pets & Animals',
            '17 - Sports',
            '19 - Travel & Events',
            '20 - Gaming',
            '22 - People & Blogs',
            '23 - Comedy',
            '24 - Entertainment',
            '25 - News & Politics',
            '26 - Howto & Style',
            '27 - Education',
            '28 - Science & Technology'
        )
        self.category_combo.set('22 - People & Blogs')
        self.category_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(20, 10))
        
        # 保存模板按钮
        save_btn = ttk.Button(
            button_frame,
            text="保存模板",
            command=self._save_template
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 加载模板按钮
        load_btn = ttk.Button(
            button_frame,
            text="加载模板",
            command=self._load_template
        )
        load_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 重置按钮
        reset_btn = ttk.Button(
            button_frame,
            text="重置默认",
            command=self._reset_template
        )
        reset_btn.pack(side=tk.LEFT)
    
    def setup_events(self):
        """设置事件绑定"""
        if self.event_manager:
            # 订阅文件相关事件
            self.event_manager.subscribe('files_validation_requested', self._validate_files)
            self.event_manager.subscribe('video_info_requested', self._provide_video_info)
    
    def _select_files(self):
        """选择视频文件"""
        try:
            file_types = [
                ('视频文件', '*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v'),
                ('MP4文件', '*.mp4'),
                ('AVI文件', '*.avi'),
                ('MOV文件', '*.mov'),
                ('所有文件', '*.*')
            ]
            
            files = filedialog.askopenfilenames(
                title="选择视频文件",
                filetypes=file_types
            )
            
            if files:
                self._add_files(files)
                
        except Exception as e:
            messagebox.showerror("错误", f"选择文件失败: {e}")
    
    def _select_folder(self):
        """选择文件夹"""
        try:
            folder = filedialog.askdirectory(title="选择包含视频文件的文件夹")
            
            if folder:
                # 扫描文件夹中的视频文件
                video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
                video_files = []
                
                for root, dirs, files in os.walk(folder):
                    for file in files:
                        if os.path.splitext(file.lower())[1] in video_extensions:
                            video_files.append(os.path.join(root, file))
                
                if video_files:
                    self._add_files(video_files)
                    messagebox.showinfo("完成", f"从文件夹中找到 {len(video_files)} 个视频文件")
                else:
                    messagebox.showwarning("警告", "在选择的文件夹中没有找到视频文件")
                    
        except Exception as e:
            messagebox.showerror("错误", f"选择文件夹失败: {e}")
    
    def _add_files(self, file_paths: List[str]):
        """添加文件到列表"""
        try:
            added_count = 0
            
            for file_path in file_paths:
                if file_path not in self.selected_files:
                    # 验证文件
                    if self.file_manager and not self.file_manager.validate_video_file(file_path):
                        continue
                    
                    self.selected_files.append(file_path)
                    
                    # 添加到树形视图
                    filename = os.path.basename(file_path)
                    file_size = self._get_file_size_str(file_path)
                    
                    self.file_tree.insert('', tk.END, values=(filename, file_size, file_path))
                    added_count += 1
            
            if added_count > 0:
                self.update_file_stats()
                
                # 发布文件选择事件
                if self.event_manager:
                    self.event_manager.publish('files_selected', {
                        'files': self.selected_files.copy(),
                        'count': len(self.selected_files)
                    })
            
        except Exception as e:
            messagebox.showerror("错误", f"添加文件失败: {e}")
    
    def _clear_files(self):
        """清空文件列表"""
        try:
            if self.selected_files and messagebox.askyesno("确认", "确定要清空文件列表吗？"):
                self.selected_files.clear()
                
                # 清空树形视图
                for item in self.file_tree.get_children():
                    self.file_tree.delete(item)
                
                self.update_file_stats()
                
                # 发布文件清空事件
                if self.event_manager:
                    self.event_manager.publish('files_cleared', {})
                    
        except Exception as e:
            messagebox.showerror("错误", f"清空文件列表失败: {e}")
    
    def _remove_selected(self):
        """移除选中的文件"""
        try:
            selected_items = self.file_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要移除的文件")
                return
            
            # 获取选中文件的路径
            files_to_remove = []
            for item in selected_items:
                file_path = self.file_tree.item(item)['values'][2]  # 完整路径在第3列
                files_to_remove.append(file_path)
            
            # 从列表中移除
            for file_path in files_to_remove:
                if file_path in self.selected_files:
                    self.selected_files.remove(file_path)
            
            # 从树形视图中移除
            for item in selected_items:
                self.file_tree.delete(item)
            
            self.update_file_stats()
            
            # 发布文件变更事件
            if self.event_manager:
                self.event_manager.publish('files_changed', {
                    'files': self.selected_files.copy(),
                    'count': len(self.selected_files)
                })
                
        except Exception as e:
            messagebox.showerror("错误", f"移除文件失败: {e}")
    
    def _on_file_double_click(self, event):
        """文件双击事件"""
        try:
            selected_item = self.file_tree.selection()[0]
            file_path = self.file_tree.item(selected_item)['values'][2]
            
            # 显示文件详细信息
            self._show_file_info(file_path)
            
        except (IndexError, KeyError):
            pass
        except Exception as e:
            messagebox.showerror("错误", f"显示文件信息失败: {e}")
    
    def _show_file_info(self, file_path: str):
        """显示文件详细信息"""
        try:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在")
                return
            
            # 获取文件信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            
            import datetime
            modified_time = datetime.datetime.fromtimestamp(file_stat.st_mtime)
            
            # 创建信息窗口
            info_window = tk.Toplevel(self.parent)
            info_window.title("文件信息")
            info_window.geometry("500x300")
            info_window.resizable(False, False)
            
            # 文件信息内容
            info_text = f"""文件路径: {file_path}
文件名: {os.path.basename(file_path)}
文件大小: {self._format_file_size(file_size)}
修改时间: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}
文件扩展名: {os.path.splitext(file_path)[1]}
"""
            
            text_widget = tk.Text(info_window, wrap=tk.WORD, padx=10, pady=10)
            text_widget.pack(fill=tk.BOTH, expand=True)
            text_widget.insert(tk.END, info_text)
            text_widget.config(state=tk.DISABLED)
            
            # 关闭按钮
            close_btn = ttk.Button(info_window, text="关闭", command=info_window.destroy)
            close_btn.pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("错误", f"显示文件信息失败: {e}")
    
    def _get_file_size_str(self, file_path: str) -> str:
        """获取文件大小字符串"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                return self._format_file_size(size)
            return "未知"
        except:
            return "错误"
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def update_file_stats(self):
        """更新文件统计信息"""
        try:
            file_count = len(self.selected_files)
            total_size = 0
            
            for file_path in self.selected_files:
                try:
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
                except:
                    continue
            
            self.file_count_label.config(text=f"文件数: {file_count}")
            self.total_size_label.config(text=f"总大小: {self._format_file_size(total_size)}")
            
        except Exception as e:
            print(f"更新文件统计失败: {e}")
    
    def _save_template(self):
        """保存视频信息模板"""
        try:
            # 获取当前配置
            current_config = self._get_current_video_info()
            
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存视频信息模板",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if file_path:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("完成", "模板保存成功")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存模板失败: {e}")
    
    def _load_template(self):
        """加载视频信息模板"""
        try:
            file_path = filedialog.askopenfilename(
                title="加载视频信息模板",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if file_path:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)
                
                # 应用模板配置
                self._apply_video_info(template_config)
                messagebox.showinfo("完成", "模板加载成功")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载模板失败: {e}")
    
    def _reset_template(self):
        """重置为默认模板"""
        try:
            if messagebox.askyesno("确认", "确定要重置为默认模板吗？"):
                default_config = {
                    'title_template': '自动上传视频 - {filename}',
                    'description_template': '通过HubStudio自动上传的视频',
                    'tags': ['自动上传', 'HubStudio'],
                    'privacy': 'private',
                    'category': '22'
                }
                
                self._apply_video_info(default_config)
                messagebox.showinfo("完成", "已重置为默认模板")
                
        except Exception as e:
            messagebox.showerror("错误", f"重置模板失败: {e}")
    
    def _get_current_video_info(self) -> Dict[str, Any]:
        """获取当前视频信息配置"""
        try:
            # 解析分类
            category_text = self.category_combo.get()
            category_id = category_text.split(' - ')[0] if ' - ' in category_text else '22'
            
            # 解析标签
            tags_text = self.tags_entry.get().strip()
            tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []
            
            return {
                'title_template': self.title_entry.get(),
                'description_template': self.description_text.get(1.0, tk.END).strip(),
                'tags': tags,
                'privacy': self.privacy_combo.get(),
                'category': category_id
            }
        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return self.video_info.copy()
    
    def _apply_video_info(self, config: Dict[str, Any]):
        """应用视频信息配置"""
        try:
            # 更新界面控件
            if 'title_template' in config:
                self.title_entry.delete(0, tk.END)
                self.title_entry.insert(0, config['title_template'])
            
            if 'description_template' in config:
                self.description_text.delete(1.0, tk.END)
                self.description_text.insert(tk.END, config['description_template'])
            
            if 'tags' in config:
                tags_text = ', '.join(config['tags']) if isinstance(config['tags'], list) else str(config['tags'])
                self.tags_entry.delete(0, tk.END)
                self.tags_entry.insert(0, tags_text)
            
            if 'privacy' in config:
                self.privacy_combo.set(config['privacy'])
            
            if 'category' in config:
                # 查找对应的分类选项
                category_id = str(config['category'])
                for value in self.category_combo['values']:
                    if value.startswith(category_id + ' - '):
                        self.category_combo.set(value)
                        break
            
            # 更新内部配置
            self.video_info.update(config)
            
        except Exception as e:
            print(f"应用视频信息失败: {e}")
    
    def _validate_files(self, data):
        """验证文件"""
        try:
            if not self.selected_files:
                if self.event_manager:
                    self.event_manager.publish('files_validation_result', {
                        'valid': False,
                        'message': '请先选择要上传的视频文件'
                    })
                return
            
            # 验证每个文件
            invalid_files = []
            for file_path in self.selected_files:
                if not os.path.exists(file_path):
                    invalid_files.append(f"{file_path} (文件不存在)")
                elif self.file_manager and not self.file_manager.validate_video_file(file_path):
                    invalid_files.append(f"{file_path} (文件格式不支持)")
            
            if invalid_files:
                if self.event_manager:
                    self.event_manager.publish('files_validation_result', {
                        'valid': False,
                        'message': f'以下文件无效:\n' + '\n'.join(invalid_files[:5])  # 最多显示5个
                    })
            else:
                if self.event_manager:
                    self.event_manager.publish('files_validation_result', {
                        'valid': True,
                        'message': f'所有 {len(self.selected_files)} 个文件验证通过'
                    })
                    
        except Exception as e:
            if self.event_manager:
                self.event_manager.publish('files_validation_result', {
                    'valid': False,
                    'message': f'文件验证失败: {e}'
                })
    
    def _provide_video_info(self, data):
        """提供视频信息"""
        try:
            video_info = self._get_current_video_info()
            
            if self.event_manager:
                self.event_manager.publish('video_info_provided', {
                    'video_info': video_info,
                    'files': self.selected_files.copy()
                })
                
        except Exception as e:
            print(f"提供视频信息失败: {e}")
    
    # ==================== 公共接口 ====================
    
    def get_selected_files(self) -> List[str]:
        """获取选中的文件列表"""
        return self.selected_files.copy()
    
    def get_video_info(self) -> Dict[str, Any]:
        """获取视频信息配置"""
        return self._get_current_video_info()
    
    def set_video_info(self, config: Dict[str, Any]):
        """设置视频信息配置"""
        self._apply_video_info(config)
    
    def add_files(self, file_paths: List[str]):
        """添加文件（公共接口）"""
        self._add_files(file_paths)
    
    def clear_files(self):
        """清空文件列表（公共接口）"""
        self._clear_files()
    
    def get_file_count(self) -> int:
        """获取文件数量"""
        return len(self.selected_files)
    
    def get_total_size(self) -> int:
        """获取总文件大小（字节）"""
        total_size = 0
        for file_path in self.selected_files:
            try:
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
            except:
                continue
        return total_size
    
    def is_ready_for_upload(self) -> bool:
        """检查是否准备好上传"""
        return len(self.selected_files) > 0
    
    def get_upload_data(self) -> Dict[str, Any]:
        """获取上传数据"""
        return {
            'files': self.selected_files.copy(),
            'video_info': self._get_current_video_info(),
            'file_count': len(self.selected_files),
            'total_size': self.get_total_size()
        }
