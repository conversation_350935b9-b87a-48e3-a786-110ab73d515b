"""
配置管理器 - 提供统一的配置管理服务
"""

import configparser
import threading
import os
from typing import Any, Dict, List, Callable, Optional
from dataclasses import dataclass
from enum import Enum


class ConfigChangeType(Enum):
    """配置变更类型"""
    ADDED = "added"
    MODIFIED = "modified"
    DELETED = "deleted"


@dataclass
class ConfigChange:
    """配置变更事件"""
    change_type: ConfigChangeType
    section: str
    key: str
    old_value: Any = None
    new_value: Any = None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.observers = []
        self._lock = threading.RLock()
        self._cache = {}
        
        # 加载配置
        self.load()
        
        # 设置默认配置
        self._ensure_default_config()
    
    def _ensure_default_config(self):
        """确保默认配置存在"""
        defaults = {
            'BROWSER': {
                'api_url': 'http://127.0.0.1:50325',
                'wait_timeout': '30',
                'retry_count': '3'
            },
            'HUBSTUDIO': {
                'api_id': '',
                'api_secret': '',
                'environment_type': 'chrome'
            },
            'VIDEO': {
                'default_title': '自动上传视频',
                'default_description': '这是一个自动上传的视频',
                'default_tags': 'YouTube,自动上传',
                'privacy': 'private'
            },
            'AUTOMATION': {
                'upload_interval': '5',
                'max_concurrent': '3',
                'max_browsers': '5',
                'auto_retry': 'true',
                'retry_delay': '10'
            },
            'LOGGING': {
                'level': 'INFO',
                'max_memory_logs': '1000',
                'log_to_file': 'true',
                'log_rotation': '1 day'
            }
        }
        
        # 添加缺失的配置
        for section_name, section_data in defaults.items():
            if not self.config.has_section(section_name):
                self.config.add_section(section_name)
            
            for key, value in section_data.items():
                if not self.config.has_option(section_name, key):
                    self.config.set(section_name, key, value)
        
        # 保存默认配置
        self.save()
    
    def load(self) -> bool:
        """加载配置文件"""
        try:
            with self._lock:
                if os.path.exists(self.config_file):
                    self.config.read(self.config_file, encoding='utf-8')
                    self._update_cache()
                    return True
                else:
                    # 创建空配置文件
                    self.config = configparser.ConfigParser()
                    return False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def save(self) -> bool:
        """保存配置文件"""
        try:
            with self._lock:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    self.config.write(f)
                self._update_cache()
                return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _update_cache(self):
        """更新缓存"""
        self._cache.clear()
        for section_name in self.config.sections():
            self._cache[section_name] = dict(self.config[section_name])
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            with self._lock:
                if section in self._cache and key in self._cache[section]:
                    value = self._cache[section][key]
                    # 尝试转换数据类型
                    return self._convert_value(value)
                return default
        except Exception:
            return default
    
    def _convert_value(self, value: str) -> Any:
        """转换配置值的数据类型"""
        if not isinstance(value, str):
            return value
        
        # 布尔值转换
        if value.lower() in ('true', 'yes', '1', 'on'):
            return True
        elif value.lower() in ('false', 'no', '0', 'off'):
            return False
        
        # 数字转换
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 返回原始字符串
        return value
    
    def set(self, section: str, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            with self._lock:
                # 获取旧值
                old_value = self.get(section, key)
                
                # 确保section存在
                if not self.config.has_section(section):
                    self.config.add_section(section)
                
                # 设置新值
                str_value = str(value)
                self.config.set(section, key, str_value)
                
                # 更新缓存
                if section not in self._cache:
                    self._cache[section] = {}
                self._cache[section][key] = str_value
                
                # 通知观察者
                change_type = ConfigChangeType.MODIFIED if old_value is not None else ConfigChangeType.ADDED
                self._notify_observers(ConfigChange(
                    change_type=change_type,
                    section=section,
                    key=key,
                    old_value=old_value,
                    new_value=self._convert_value(str_value)
                ))
                
                return True
        except Exception as e:
            print(f"设置配置失败: {e}")
            return False
    
    def remove(self, section: str, key: str = None) -> bool:
        """删除配置项或整个section"""
        try:
            with self._lock:
                if key is None:
                    # 删除整个section
                    if self.config.has_section(section):
                        old_section = dict(self.config[section])
                        self.config.remove_section(section)
                        
                        # 更新缓存
                        if section in self._cache:
                            del self._cache[section]
                        
                        # 通知观察者
                        for k, v in old_section.items():
                            self._notify_observers(ConfigChange(
                                change_type=ConfigChangeType.DELETED,
                                section=section,
                                key=k,
                                old_value=self._convert_value(v)
                            ))
                        return True
                else:
                    # 删除特定key
                    if self.config.has_option(section, key):
                        old_value = self.get(section, key)
                        self.config.remove_option(section, key)
                        
                        # 更新缓存
                        if section in self._cache and key in self._cache[section]:
                            del self._cache[section][key]
                        
                        # 通知观察者
                        self._notify_observers(ConfigChange(
                            change_type=ConfigChangeType.DELETED,
                            section=section,
                            key=key,
                            old_value=old_value
                        ))
                        return True
                return False
        except Exception as e:
            print(f"删除配置失败: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个section的配置"""
        with self._lock:
            if section in self._cache:
                return {k: self._convert_value(v) for k, v in self._cache[section].items()}
            return {}
    
    def get_all_sections(self) -> List[str]:
        """获取所有section名称"""
        with self._lock:
            return list(self._cache.keys())
    
    def has_section(self, section: str) -> bool:
        """检查section是否存在"""
        with self._lock:
            return section in self._cache
    
    def has_option(self, section: str, key: str) -> bool:
        """检查配置项是否存在"""
        with self._lock:
            return section in self._cache and key in self._cache[section]
    
    def add_observer(self, observer: Callable[[ConfigChange], None]):
        """添加配置变更观察者"""
        if observer not in self.observers:
            self.observers.append(observer)
    
    def remove_observer(self, observer: Callable[[ConfigChange], None]):
        """移除配置变更观察者"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def _notify_observers(self, change: ConfigChange):
        """通知观察者配置变更"""
        for observer in self.observers:
            try:
                observer(change)
            except Exception as e:
                print(f"配置观察者错误: {e}")
    
    def reload(self) -> bool:
        """重新加载配置文件"""
        return self.load()
    
    def backup(self, backup_file: str) -> bool:
        """备份配置文件"""
        try:
            import shutil
            shutil.copy2(self.config_file, backup_file)
            return True
        except Exception as e:
            print(f"备份配置文件失败: {e}")
            return False
    
    def restore(self, backup_file: str) -> bool:
        """从备份恢复配置文件"""
        try:
            import shutil
            shutil.copy2(backup_file, self.config_file)
            return self.load()
        except Exception as e:
            print(f"恢复配置文件失败: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """获取配置统计信息"""
        with self._lock:
            total_sections = len(self._cache)
            total_options = sum(len(section) for section in self._cache.values())
            
            return {
                'total_sections': total_sections,
                'total_options': total_options,
                'sections': list(self._cache.keys()),
                'file_exists': os.path.exists(self.config_file),
                'file_size': os.path.getsize(self.config_file) if os.path.exists(self.config_file) else 0
            }