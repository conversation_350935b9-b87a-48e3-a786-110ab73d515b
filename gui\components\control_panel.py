"""
控制面板组件 - 负责上传控制和并发设置
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Dict, Any, Optional


class ControlPanel:
    """控制面板组件"""
    
    def __init__(self, parent, event_manager, upload_manager, 
                 concurrency_manager, state_manager):
        self.parent = parent
        self.event_manager = event_manager
        self.upload_manager = upload_manager
        self.concurrency_manager = concurrency_manager
        self.state_manager = state_manager
        
        # 界面变量
        self.concurrent_count_var = tk.IntVar(value=3)
        self.max_browsers_var = tk.IntVar(value=5)
        self.upload_interval_var = tk.StringVar(value="5")
        self.timeout_var = tk.StringVar(value="30")
        self.batch_size_var = tk.IntVar(value=3)
        
        # 控件引用
        self.upload_btn = None
        self.stop_btn = None
        self.pause_btn = None
        self.resume_btn = None
        self.concurrent_spinbox = None
        self.browser_spinbox = None
        
        # 状态变量
        self.is_uploading = False
        self.is_paused = False
        
        self.create_widgets()
        self.setup_events()
        self.setup_state_bindings()
        self.update_button_states()
    
    def create_widgets(self):
        """创建控制面板界面"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="上传控制", padding="15")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建并发设置区域
        self._create_concurrency_section()
        
        # 创建上传设置区域
        self._create_upload_settings_section()
        
        # 创建控制按钮区域
        self._create_control_buttons_section()
        
        # 创建状态显示区域
        self._create_status_section()
    
    def _create_concurrency_section(self):
        """创建并发设置区域"""
        # 并发设置框架
        concurrency_frame = ttk.LabelFrame(self.main_frame, text="并发设置", padding="10")
        concurrency_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 并发数设置
        concurrent_frame = ttk.Frame(concurrency_frame)
        concurrent_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(concurrent_frame, text="并发上传数:").pack(side=tk.LEFT)
        self.concurrent_spinbox = ttk.Spinbox(
            concurrent_frame,
            from_=1, to=10,
            width=5,
            textvariable=self.concurrent_count_var,
            command=self._on_concurrent_count_changed
        )
        self.concurrent_spinbox.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(concurrent_frame, text="个", foreground="gray").pack(side=tk.LEFT)
        
        # 浏览器数设置
        browser_frame = ttk.Frame(concurrency_frame)
        browser_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(browser_frame, text="最大浏览器数:").pack(side=tk.LEFT)
        self.browser_spinbox = ttk.Spinbox(
            browser_frame,
            from_=1, to=20,
            width=5,
            textvariable=self.max_browsers_var,
            command=self._on_max_browsers_changed
        )
        self.browser_spinbox.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(browser_frame, text="个", foreground="gray").pack(side=tk.LEFT)
    
    def _create_upload_settings_section(self):
        """创建上传设置区域"""
        # 上传设置框架
        upload_frame = ttk.LabelFrame(self.main_frame, text="上传设置", padding="10")
        upload_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：批次大小和上传间隔
        row1_frame = ttk.Frame(upload_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 批次大小
        ttk.Label(row1_frame, text="批次大小:").pack(side=tk.LEFT)
        batch_spinbox = ttk.Spinbox(
            row1_frame,
            from_=1, to=10,
            width=5,
            textvariable=self.batch_size_var
        )
        batch_spinbox.pack(side=tk.LEFT, padx=(5, 10))
        ttk.Label(row1_frame, text="个/批", foreground="gray").pack(side=tk.LEFT, padx=(0, 20))
        
        # 上传间隔
        ttk.Label(row1_frame, text="上传间隔:").pack(side=tk.LEFT)
        interval_spinbox = ttk.Spinbox(
            row1_frame,
            from_=1, to=60,
            width=5,
            textvariable=self.upload_interval_var
        )
        interval_spinbox.pack(side=tk.LEFT, padx=(5, 10))
        ttk.Label(row1_frame, text="秒", foreground="gray").pack(side=tk.LEFT)
        
        # 第二行：超时设置
        row2_frame = ttk.Frame(upload_frame)
        row2_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(row2_frame, text="上传超时:").pack(side=tk.LEFT)
        timeout_spinbox = ttk.Spinbox(
            row2_frame,
            from_=30, to=1800,
            width=8,
            textvariable=self.timeout_var
        )
        timeout_spinbox.pack(side=tk.LEFT, padx=(5, 10))
        ttk.Label(row2_frame, text="秒", foreground="gray").pack(side=tk.LEFT)
    
    def _create_control_buttons_section(self):
        """创建控制按钮区域"""
        # 控制按钮框架
        control_frame = ttk.LabelFrame(self.main_frame, text="上传控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮容器
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        # 开始上传按钮
        self.upload_btn = ttk.Button(
            button_frame,
            text="开始上传",
            command=self._on_start_upload,
            style="Accent.TButton"
        )
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 暂停按钮
        self.pause_btn = ttk.Button(
            button_frame,
            text="暂停上传",
            command=self._on_pause_upload
        )
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 恢复按钮
        self.resume_btn = ttk.Button(
            button_frame,
            text="恢复上传",
            command=self._on_resume_upload
        )
        self.resume_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 停止按钮
        self.stop_btn = ttk.Button(
            button_frame,
            text="停止上传",
            command=self._on_stop_upload
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清理按钮
        clear_btn = ttk.Button(
            button_frame,
            text="清理会话",
            command=self._on_clear_sessions
        )
        clear_btn.pack(side=tk.RIGHT)
    
    def _create_status_section(self):
        """创建状态显示区域"""
        # 状态显示框架
        status_frame = ttk.LabelFrame(self.main_frame, text="当前状态", padding="10")
        status_frame.pack(fill=tk.X)
        
        # 状态信息
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X)
        
        # 左侧状态
        left_frame = ttk.Frame(info_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.status_label = ttk.Label(left_frame, text="状态: 就绪", font=("Arial", 9))
        self.status_label.pack(anchor=tk.W)
        
        self.progress_label = ttk.Label(left_frame, text="进度: 0/0", font=("Arial", 9))
        self.progress_label.pack(anchor=tk.W)
        
        # 右侧统计
        right_frame = ttk.Frame(info_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.active_tasks_label = ttk.Label(right_frame, text="活动任务: 0", font=("Arial", 9))
        self.active_tasks_label.pack(anchor=tk.E)
        
        self.queue_size_label = ttk.Label(right_frame, text="队列大小: 0", font=("Arial", 9))
        self.queue_size_label.pack(anchor=tk.E)
    
    def setup_events(self):
        """设置事件绑定"""
        if self.event_manager:
            # 订阅上传相关事件
            self.event_manager.subscribe('upload_session_created', self._on_upload_session_created)
            self.event_manager.subscribe('upload_session_started', self._on_upload_session_started)
            self.event_manager.subscribe('upload_session_paused', self._on_upload_session_paused)
            self.event_manager.subscribe('upload_session_resumed', self._on_upload_session_resumed)
            self.event_manager.subscribe('upload_session_completed', self._on_upload_session_completed)
            self.event_manager.subscribe('upload_session_failed', self._on_upload_session_failed)
            self.event_manager.subscribe('upload_session_cancelled', self._on_upload_session_cancelled)
            
            # 订阅并发统计更新事件
            self.event_manager.subscribe('concurrency_stats_updated', self._on_concurrency_stats_updated)
    
    def setup_state_bindings(self):
        """设置状态绑定"""
        if self.state_manager:
            # 监听状态变化
            self.state_manager.add_observer('upload_status', self._on_upload_status_changed)
            self.state_manager.add_observer('current_session', self._on_current_session_changed)
    
    def _on_concurrent_count_changed(self):
        """并发数变化事件"""
        try:
            new_count = self.concurrent_count_var.get()
            if self.concurrency_manager:
                self.concurrency_manager.update_max_concurrent(new_count)
            
            if self.event_manager:
                self.event_manager.publish('concurrency_settings_changed', {
                    'max_concurrent': new_count
                })
        except Exception as e:
            messagebox.showerror("错误", f"更新并发数失败: {e}")
    
    def _on_max_browsers_changed(self):
        """最大浏览器数变化事件"""
        try:
            new_count = self.max_browsers_var.get()
            if self.concurrency_manager:
                self.concurrency_manager.update_max_browsers(new_count)
            
            if self.event_manager:
                self.event_manager.publish('concurrency_settings_changed', {
                    'max_browsers': new_count
                })
        except Exception as e:
            messagebox.showerror("错误", f"更新最大浏览器数失败: {e}")
    
    def _on_start_upload(self):
        """开始上传事件"""
        try:
            # 获取上传配置
            upload_config = {
                'batch_size': self.batch_size_var.get(),
                'upload_interval': int(self.upload_interval_var.get()),
                'upload_timeout': int(self.timeout_var.get()),
                'concurrent_count': self.concurrent_count_var.get(),
                'max_browsers': self.max_browsers_var.get()
            }
            
            if self.event_manager:
                self.event_manager.publish('start_upload_requested', upload_config)
        except Exception as e:
            messagebox.showerror("错误", f"开始上传失败: {e}")
    
    def _on_pause_upload(self):
        """暂停上传事件"""
        try:
            if self.event_manager:
                self.event_manager.publish('pause_upload_requested', {})
        except Exception as e:
            messagebox.showerror("错误", f"暂停上传失败: {e}")
    
    def _on_resume_upload(self):
        """恢复上传事件"""
        try:
            if self.event_manager:
                self.event_manager.publish('resume_upload_requested', {})
        except Exception as e:
            messagebox.showerror("错误", f"恢复上传失败: {e}")
    
    def _on_stop_upload(self):
        """停止上传事件"""
        try:
            if messagebox.askyesno("确认", "确定要停止当前上传任务吗？"):
                if self.event_manager:
                    self.event_manager.publish('stop_upload_requested', {})
        except Exception as e:
            messagebox.showerror("错误", f"停止上传失败: {e}")
    
    def _on_clear_sessions(self):
        """清理会话事件"""
        try:
            if messagebox.askyesno("确认", "确定要清理已完成的上传会话吗？"):
                if self.upload_manager:
                    count = self.upload_manager.clear_completed_sessions()
                    messagebox.showinfo("完成", f"已清理 {count} 个会话")
        except Exception as e:
            messagebox.showerror("错误", f"清理会话失败: {e}")
    
    # ==================== 事件处理方法 ====================
    
    def _on_upload_session_created(self, session):
        """上传会话创建事件"""
        self.is_uploading = False
        self.is_paused = False
        self.update_button_states()
        self._update_status("上传会话已创建")
    
    def _on_upload_session_started(self, session):
        """上传会话开始事件"""
        self.is_uploading = True
        self.is_paused = False
        self.update_button_states()
        self._update_status("正在上传...")
        self._update_progress(session)
    
    def _on_upload_session_paused(self, session):
        """上传会话暂停事件"""
        self.is_uploading = True
        self.is_paused = True
        self.update_button_states()
        self._update_status("上传已暂停")
    
    def _on_upload_session_resumed(self, session):
        """上传会话恢复事件"""
        self.is_uploading = True
        self.is_paused = False
        self.update_button_states()
        self._update_status("上传已恢复")
    
    def _on_upload_session_completed(self, session):
        """上传会话完成事件"""
        self.is_uploading = False
        self.is_paused = False
        self.update_button_states()
        self._update_status(f"上传完成 - 成功: {session.completed_files}, 失败: {session.failed_files}")
        self._update_progress(session)
        
        # 显示完成通知
        messagebox.showinfo("上传完成", 
                          f"上传任务已完成！\n"
                          f"总文件数: {session.total_files}\n"
                          f"成功上传: {session.completed_files}\n"
                          f"上传失败: {session.failed_files}")
    
    def _on_upload_session_failed(self, session):
        """上传会话失败事件"""
        self.is_uploading = False
        self.is_paused = False
        self.update_button_states()
        self._update_status(f"上传失败: {session.error_message}")
        
        # 显示错误通知
        messagebox.showerror("上传失败", f"上传任务失败：{session.error_message}")
    
    def _on_upload_session_cancelled(self, session):
        """上传会话取消事件"""
        self.is_uploading = False
        self.is_paused = False
        self.update_button_states()
        self._update_status("上传已取消")
        
        messagebox.showinfo("上传取消", "上传任务已取消")
    
    def _on_concurrency_stats_updated(self, stats):
        """并发统计更新事件"""
        try:
            active_tasks = stats.get('active_tasks', 0)
            queue_size = stats.get('queue_size', 0)
            
            self.active_tasks_label.config(text=f"活动任务: {active_tasks}")
            self.queue_size_label.config(text=f"队列大小: {queue_size}")
        except Exception as e:
            print(f"更新并发统计失败: {e}")
    
    def _on_upload_status_changed(self, old_value, new_value):
        """上传状态变化回调"""
        self._update_status(new_value)
    
    def _on_current_session_changed(self, old_value, new_value):
        """当前会话变化回调"""
        if new_value:
            self._update_progress(new_value)
    
    # ==================== 界面更新方法 ====================
    
    def update_button_states(self):
        """更新按钮状态"""
        try:
            if self.is_uploading:
                if self.is_paused:
                    # 暂停状态
                    self.upload_btn.config(state=tk.DISABLED)
                    self.pause_btn.config(state=tk.DISABLED)
                    self.resume_btn.config(state=tk.NORMAL)
                    self.stop_btn.config(state=tk.NORMAL)
                else:
                    # 上传中状态
                    self.upload_btn.config(state=tk.DISABLED)
                    self.pause_btn.config(state=tk.NORMAL)
                    self.resume_btn.config(state=tk.DISABLED)
                    self.stop_btn.config(state=tk.NORMAL)
            else:
                # 空闲状态
                self.upload_btn.config(state=tk.NORMAL)
                self.pause_btn.config(state=tk.DISABLED)
                self.resume_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.DISABLED)
            
            # 设置控制参数的可用性
            settings_state = tk.DISABLED if self.is_uploading else tk.NORMAL
            self.concurrent_spinbox.config(state=settings_state)
            self.browser_spinbox.config(state=settings_state)
            
        except Exception as e:
            print(f"更新按钮状态失败: {e}")
    
    def _update_status(self, status_text: str):
        """更新状态显示"""
        try:
            self.status_label.config(text=f"状态: {status_text}")
        except Exception as e:
            print(f"更新状态显示失败: {e}")
    
    def _update_progress(self, session):
        """更新进度显示"""
        try:
            if session:
                completed = session.completed_files
                total = session.total_files
                failed = session.failed_files
                
                progress_text = f"进度: {completed}/{total}"
                if failed > 0:
                    progress_text += f" (失败: {failed})"
                
                self.progress_label.config(text=progress_text)
            else:
                self.progress_label.config(text="进度: 0/0")
        except Exception as e:
            print(f"更新进度显示失败: {e}")
    
    # ==================== 公共接口 ====================
    
    def get_upload_config(self) -> Dict[str, Any]:
        """获取当前上传配置"""
        return {
            'batch_size': self.batch_size_var.get(),
            'upload_interval': int(self.upload_interval_var.get()),
            'upload_timeout': int(self.timeout_var.get()),
            'concurrent_count': self.concurrent_count_var.get(),
            'max_browsers': self.max_browsers_var.get()
        }
    
    def set_upload_config(self, config: Dict[str, Any]):
        """设置上传配置"""
        try:
            if 'batch_size' in config:
                self.batch_size_var.set(config['batch_size'])
            if 'upload_interval' in config:
                self.upload_interval_var.set(str(config['upload_interval']))
            if 'upload_timeout' in config:
                self.timeout_var.set(str(config['upload_timeout']))
            if 'concurrent_count' in config:
                self.concurrent_count_var.set(config['concurrent_count'])
            if 'max_browsers' in config:
                self.max_browsers_var.set(config['max_browsers'])
        except Exception as e:
            print(f"设置上传配置失败: {e}")
    
    def enable_controls(self, enabled: bool = True):
        """启用/禁用控件"""
        state = tk.NORMAL if enabled else tk.DISABLED
        
        try:
            self.concurrent_spinbox.config(state=state)
            self.browser_spinbox.config(state=state)
            
            if not self.is_uploading:
                self.upload_btn.config(state=state)
        except Exception as e:
            print(f"设置控件状态失败: {e}")
    
    def reset_to_default(self):
        """重置为默认设置"""
        self.concurrent_count_var.set(3)
        self.max_browsers_var.set(5)
        self.upload_interval_var.set("5")
        self.timeout_var.set("30")
        self.batch_size_var.set(3)
        
        self.is_uploading = False
        self.is_paused = False
        self.update_button_states()
        self._update_status("就绪")
        self._update_progress(None)