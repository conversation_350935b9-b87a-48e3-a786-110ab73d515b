"""
环境管理器 - 管理HubStudio环境的生命周期
"""

import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import asyncio


class EnvironmentStatus(Enum):
    """环境状态枚举"""
    UNKNOWN = "unknown"
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class ManagedEnvironment:
    """托管环境信息"""
    environment_id: str
    name: str = ""
    status: EnvironmentStatus = EnvironmentStatus.UNKNOWN
    browser_port: int = 0
    debug_port: int = 0
    created_at: float = field(default_factory=time.time)
    started_at: float = 0.0
    stopped_at: float = 0.0
    last_health_check: float = 0.0
    error_message: str = ""
    retry_count: int = 0
    max_retries: int = 3
    auto_restart: bool = True
    metadata: Dict = field(default_factory=dict)
    
    # 使用统计
    task_count: int = 0
    total_tasks: int = 0
    last_used_at: float = field(default_factory=time.time)


class EnvironmentManager:
    """环境管理器"""
    
    def __init__(self, hubstudio_api, state_manager=None, logger=None):
        self.hubstudio_api = hubstudio_api
        self.state_manager = state_manager
        self.logger = logger
        
        # 环境管理
        self._environments = {}  # environment_id -> ManagedEnvironment
        self._environment_lock = threading.RLock()
        
        # 健康检查
        self._health_check_interval = 30  # 30秒检查一次
        self._health_check_thread = None
        self._health_check_running = False
        
        # 自动重启
        self._auto_restart_enabled = True
        self._restart_delay = 10  # 重启延迟10秒
        
        # 负载均衡
        self._load_balancing_enabled = True
        self._max_tasks_per_environment = 5
        
        # 观察者
        self._observers = []
        
        # 统计信息
        self._stats = {
            'total_environments': 0,
            'running_environments': 0,
            'failed_environments': 0,
            'total_restarts': 0,
            'total_tasks_assigned': 0
        }
        
        self._running = False
        
        self._log('info', '环境管理器初始化完成')
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[环境管理器] {message}")
    
    def add_observer(self, observer: Callable[[str, ManagedEnvironment], None]):
        """添加环境状态变更观察者"""
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer: Callable[[str, ManagedEnvironment], None]):
        """移除环境状态变更观察者"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self, event_type: str, environment: ManagedEnvironment):
        """通知观察者"""
        for observer in self._observers:
            try:
                observer(event_type, environment)
            except Exception as e:
                self._log('error', f'通知观察者失败: {e}')
    
    def start(self):
        """启动环境管理器"""
        if self._running:
            return
        
        self._running = True
        
        # 启动健康检查线程
        self._start_health_check()
        
        self._log('info', '环境管理器已启动')
    
    def stop(self):
        """停止环境管理器"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止健康检查
        self._stop_health_check()
        
        # 停止所有环境
        self._stop_all_environments()
        
        self._log('info', '环境管理器已停止')
    
    def _start_health_check(self):
        """启动健康检查线程"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
        
        self._health_check_running = True
        self._health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self._health_check_thread.start()
        
        self._log('info', '健康检查线程已启动')
    
    def _stop_health_check(self):
        """停止健康检查线程"""
        self._health_check_running = False
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
    
    def _health_check_loop(self):
        """健康检查循环"""
        while self._health_check_running:
            try:
                self._perform_health_checks()
                time.sleep(self._health_check_interval)
            except Exception as e:
                self._log('error', f'健康检查循环错误: {e}')
                time.sleep(10)
    
    def _perform_health_checks(self):
        """执行健康检查"""
        with self._environment_lock:
            for env_id, environment in self._environments.items():
                try:
                    if environment.status == EnvironmentStatus.RUNNING:
                        # 检查环境是否仍在运行
                        is_healthy = self._check_environment_health(environment)
                        environment.last_health_check = time.time()
                        
                        if not is_healthy:
                            self._log('warning', f'环境健康检查失败: {env_id}')
                            self._handle_unhealthy_environment(environment)
                        
                except Exception as e:
                    self._log('error', f'健康检查失败 {env_id}: {e}')
    
    def _check_environment_health(self, environment: ManagedEnvironment) -> bool:
        """检查单个环境的健康状态"""
        try:
            # 通过HubStudio API检查环境状态
            env_info = self.hubstudio_api.get_environment_info(environment.environment_id)
            return env_info and env_info.get('status') == 'running'
        except Exception as e:
            self._log('warning', f'检查环境健康状态失败: {e}')
            return False
    
    def _handle_unhealthy_environment(self, environment: ManagedEnvironment):
        """处理不健康的环境"""
        environment.status = EnvironmentStatus.ERROR
        environment.error_message = "健康检查失败"
        
        # 通知观察者
        self._notify_observers('environment_unhealthy', environment)
        
        # 如果启用自动重启
        if self._auto_restart_enabled and environment.auto_restart:
            if environment.retry_count < environment.max_retries:
                self._log('info', f'准备重启不健康的环境: {environment.environment_id}')
                threading.Timer(self._restart_delay, self._restart_environment, 
                              args=[environment.environment_id]).start()
            else:
                self._log('error', f'环境重启次数已达上限: {environment.environment_id}')
    
    def add_environment(self, environment_id: str, name: str = "", auto_restart: bool = True) -> bool:
        """添加环境到管理器"""
        try:
            with self._environment_lock:
                if environment_id in self._environments:
                    self._log('warning', f'环境已存在: {environment_id}')
                    return False
                
                environment = ManagedEnvironment(
                    environment_id=environment_id,
                    name=name or environment_id,
                    auto_restart=auto_restart
                )
                
                self._environments[environment_id] = environment
                self._stats['total_environments'] += 1
                
                # 通知观察者
                self._notify_observers('environment_added', environment)
                
                self._log('info', f'环境已添加: {environment_id}')
                return True
                
        except Exception as e:
            self._log('error', f'添加环境失败: {e}')
            return False
    
    def remove_environment(self, environment_id: str) -> bool:
        """从管理器中移除环境"""
        try:
            with self._environment_lock:
                if environment_id not in self._environments:
                    return False
                
                environment = self._environments[environment_id]
                
                # 如果环境正在运行，先停止它
                if environment.status == EnvironmentStatus.RUNNING:
                    self.stop_environment(environment_id)
                
                del self._environments[environment_id]
                
                # 通知观察者
                self._notify_observers('environment_removed', environment)
                
                self._log('info', f'环境已移除: {environment_id}')
                return True
                
        except Exception as e:
            self._log('error', f'移除环境失败: {e}')
            return False
    
    def start_environment(self, environment_id: str) -> bool:
        """启动环境"""
        try:
            with self._environment_lock:
                if environment_id not in self._environments:
                    self._log('error', f'环境不存在: {environment_id}')
                    return False
                
                environment = self._environments[environment_id]
                
                if environment.status == EnvironmentStatus.RUNNING:
                    self._log('info', f'环境已在运行: {environment_id}')
                    return True
                
                # 更新状态
                environment.status = EnvironmentStatus.STARTING
                environment.error_message = ""
                
                # 通知观察者
                self._notify_observers('environment_starting', environment)
                
                # 调用HubStudio API启动环境
                result = self.hubstudio_api.start_environment(environment_id)
                
                if result and result.get('success'):
                    environment.status = EnvironmentStatus.RUNNING
                    environment.started_at = time.time()
                    environment.browser_port = result.get('browser_port', 0)
                    environment.debug_port = result.get('debug_port', 0)
                    environment.retry_count = 0  # 重置重试计数
                    
                    self._stats['running_environments'] += 1
                    
                    # 通知观察者
                    self._notify_observers('environment_started', environment)
                    
                    self._log('info', f'环境启动成功: {environment_id}')
                    return True
                else:
                    environment.status = EnvironmentStatus.ERROR
                    environment.error_message = result.get('error', '启动失败') if result else '启动失败'
                    environment.retry_count += 1
                    
                    self._stats['failed_environments'] += 1
                    
                    # 通知观察者
                    self._notify_observers('environment_start_failed', environment)
                    
                    self._log('error', f'环境启动失败: {environment_id}, 错误: {environment.error_message}')
                    return False
                
        except Exception as e:
            self._log('error', f'启动环境异常: {e}')
            return False
    
    def stop_environment(self, environment_id: str) -> bool:
        """停止环境"""
        try:
            with self._environment_lock:
                if environment_id not in self._environments:
                    self._log('error', f'环境不存在: {environment_id}')
                    return False
                
                environment = self._environments[environment_id]
                
                if environment.status == EnvironmentStatus.STOPPED:
                    self._log('info', f'环境已停止: {environment_id}')
                    return True
                
                # 更新状态
                environment.status = EnvironmentStatus.STOPPING
                
                # 通知观察者
                self._notify_observers('environment_stopping', environment)
                
                # 调用HubStudio API停止环境
                result = self.hubstudio_api.stop_environment(environment_id)
                
                if result and result.get('success'):
                    environment.status = EnvironmentStatus.STOPPED
                    environment.stopped_at = time.time()
                    environment.browser_port = 0
                    environment.debug_port = 0
                    
                    if self._stats['running_environments'] > 0:
                        self._stats['running_environments'] -= 1
                    
                    # 通知观察者
                    self._notify_observers('environment_stopped', environment)
                    
                    self._log('info', f'环境停止成功: {environment_id}')
                    return True
                else:
                    environment.status = EnvironmentStatus.ERROR
                    environment.error_message = result.get('error', '停止失败') if result else '停止失败'
                    
                    # 通知观察者
                    self._notify_observers('environment_stop_failed', environment)
                    
                    self._log('error', f'环境停止失败: {environment_id}, 错误: {environment.error_message}')
                    return False
                
        except Exception as e:
            self._log('error', f'停止环境异常: {e}')
            return False
    
    def _restart_environment(self, environment_id: str) -> bool:
        """重启环境"""
        try:
            with self._environment_lock:
                if environment_id not in self._environments:
                    return False
                
                environment = self._environments[environment_id]
                environment.retry_count += 1
                
                self._log('info', f'重启环境 ({environment.retry_count}/{environment.max_retries}): {environment_id}')
                
                # 先停止环境
                self.stop_environment(environment_id)
                
                # 等待一段时间
                time.sleep(2)
                
                # 重新启动环境
                success = self.start_environment(environment_id)
                
                if success:
                    self._stats['total_restarts'] += 1
                    self._notify_observers('environment_restarted', environment)
                
                return success
                
        except Exception as e:
            self._log('error', f'重启环境异常: {e}')
            return False
    
    def get_environment(self, environment_id: str) -> Optional[ManagedEnvironment]:
        """获取环境信息"""
        with self._environment_lock:
            return self._environments.get(environment_id)
    
    def get_all_environments(self) -> List[ManagedEnvironment]:
        """获取所有环境"""
        with self._environment_lock:
            return list(self._environments.values())
    
    def get_running_environments(self) -> List[ManagedEnvironment]:
        """获取正在运行的环境"""
        with self._environment_lock:
            return [env for env in self._environments.values() 
                   if env.status == EnvironmentStatus.RUNNING]
    
    def get_available_environment(self) -> Optional[ManagedEnvironment]:
        """获取可用的环境（负载均衡）"""
        with self._environment_lock:
            if not self._load_balancing_enabled:
                # 简单返回第一个运行中的环境
                running_envs = self.get_running_environments()
                return running_envs[0] if running_envs else None
            
            # 负载均衡：选择任务数最少的环境
            available_envs = [env for env in self.get_running_environments() 
                            if env.task_count < self._max_tasks_per_environment]
            
            if not available_envs:
                return None
            
            # 按任务数排序，选择最少的
            available_envs.sort(key=lambda x: x.task_count)
            return available_envs[0]
    
    def assign_task_to_environment(self, environment_id: str) -> bool:
        """为环境分配任务"""
        with self._environment_lock:
            if environment_id not in self._environments:
                return False
            
            environment = self._environments[environment_id]
            if environment.status != EnvironmentStatus.RUNNING:
                return False
            
            environment.task_count += 1
            environment.total_tasks += 1
            environment.last_used_at = time.time()
            
            self._stats['total_tasks_assigned'] += 1
            
            return True
    
    def release_task_from_environment(self, environment_id: str) -> bool:
        """从环境释放任务"""
        with self._environment_lock:
            if environment_id not in self._environments:
                return False
            
            environment = self._environments[environment_id]
            if environment.task_count > 0:
                environment.task_count -= 1
            
            return True
    
    def _stop_all_environments(self):
        """停止所有环境"""
        with self._environment_lock:
            env_ids = list(self._environments.keys())
            for env_id in env_ids:
                try:
                    self.stop_environment(env_id)
                except Exception as e:
                    self._log('error', f'停止环境失败 {env_id}: {e}')
    
    def refresh_environments(self) -> List[Dict[str, Any]]:
        """刷新环境列表"""
        try:
            # 从HubStudio API获取环境列表
            api_environments = self.hubstudio_api.get_environments()
            
            if not api_environments:
                self._log('warning', '未获取到环境列表')
                return []
            
            # 更新本地环境状态
            with self._environment_lock:
                for api_env in api_environments:
                    env_id = api_env.get('id')
                    if not env_id:
                        continue
                    
                    if env_id not in self._environments:
                        # 新发现的环境，添加到管理器
                        self.add_environment(env_id, api_env.get('name', ''))
                    
                    # 更新环境状态
                    environment = self._environments[env_id]
                    api_status = api_env.get('status', 'unknown')
                    
                    if api_status == 'running':
                        environment.status = EnvironmentStatus.RUNNING
                        environment.browser_port = api_env.get('browser_port', 0)
                        environment.debug_port = api_env.get('debug_port', 0)
                    elif api_status == 'stopped':
                        environment.status = EnvironmentStatus.STOPPED
                    else:
                        environment.status = EnvironmentStatus.UNKNOWN
            
            self._log('info', f'已刷新 {len(api_environments)} 个环境')
            return api_environments
            
        except Exception as e:
            self._log('error', f'刷新环境列表失败: {e}')
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._environment_lock:
            running_count = len(self.get_running_environments())
            
            return {
                'total_environments': len(self._environments),
                'running_environments': running_count,
                'stopped_environments': len([e for e in self._environments.values() 
                                           if e.status == EnvironmentStatus.STOPPED]),
                'error_environments': len([e for e in self._environments.values() 
                                         if e.status == EnvironmentStatus.ERROR]),
                'total_restarts': self._stats['total_restarts'],
                'total_tasks_assigned': self._stats['total_tasks_assigned'],
                'health_check_enabled': self._health_check_running,
                'auto_restart_enabled': self._auto_restart_enabled,
                'load_balancing_enabled': self._load_balancing_enabled,
                'max_tasks_per_environment': self._max_tasks_per_environment
            }
    
    def get_environment_details(self) -> List[Dict[str, Any]]:
        """获取环境详细信息"""
        with self._environment_lock:
            details = []
            for environment in self._environments.values():
                details.append({
                    'environment_id': environment.environment_id,
                    'name': environment.name,
                    'status': environment.status.value,
                    'browser_port': environment.browser_port,
                    'debug_port': environment.debug_port,
                    'task_count': environment.task_count,
                    'total_tasks': environment.total_tasks,
                    'retry_count': environment.retry_count,
                    'auto_restart': environment.auto_restart,
                    'created_at': environment.created_at,
                    'started_at': environment.started_at,
                    'last_used_at': environment.last_used_at,
                    'last_health_check': environment.last_health_check,
                    'error_message': environment.error_message
                })
            return details
    
    def set_auto_restart(self, enabled: bool):
        """设置自动重启"""
        self._auto_restart_enabled = enabled
        self._log('info', f'自动重启已{"启用" if enabled else "禁用"}')
    
    def set_load_balancing(self, enabled: bool):
        """设置负载均衡"""
        self._load_balancing_enabled = enabled
        self._log('info', f'负载均衡已{"启用" if enabled else "禁用"}')
    
    def set_max_tasks_per_environment(self, max_tasks: int):
        """设置每个环境的最大任务数"""
        if max_tasks > 0:
            self._max_tasks_per_environment = max_tasks
            self._log('info', f'每个环境最大任务数已设置为: {max_tasks}')
    
    def force_restart_all_environments(self) -> int:
        """强制重启所有环境"""
        with self._environment_lock:
            env_ids = list(self._environments.keys())
            success_count = 0
            
            for env_id in env_ids:
                if self._restart_environment(env_id):
                    success_count += 1
            
            self._log('info', f'强制重启完成，成功: {success_count}/{len(env_ids)}')
            return success_count
    
    def cleanup_stopped_environments(self) -> int:
        """清理已停止的环境"""
        with self._environment_lock:
            stopped_envs = [env.environment_id for env in self._environments.values() 
                          if env.status == EnvironmentStatus.STOPPED]
            
            removed_count = 0
            for env_id in stopped_envs:
                if self.remove_environment(env_id):
                    removed_count += 1
            
            self._log('info', f'已清理 {removed_count} 个停止的环境')
            return removed_count
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop()
        except Exception:
            pass