import sys
from _typeshed import Readable<PERSON>uffer
from typing_extensions import TypeAlias

# Many functions in binascii accept buffer objects
# or ASCII-only strings.
_AsciiBuffer: TypeAlias = str | ReadableBuffer

def a2b_uu(data: _AsciiBuffer, /) -> bytes: ...
def b2a_uu(data: ReadableBuffer, /, *, backtick: bool = False) -> bytes: ...

if sys.version_info >= (3, 11):
    def a2b_base64(data: _AsciiBuffer, /, *, strict_mode: bool = False) -> bytes: ...

else:
    def a2b_base64(data: _AsciiBuffer, /) -> bytes: ...

def b2a_base64(data: ReadableBuffer, /, *, newline: bool = True) -> bytes: ...
def a2b_qp(data: _AsciiBuffer, header: bool = False) -> bytes: ...
def b2a_qp(data: ReadableBuffer, quotetabs: bool = False, istext: bool = True, header: bool = False) -> bytes: ...

if sys.version_info < (3, 11):
    def a2b_hqx(data: _AsciiBuffer, /) -> bytes: ...
    def rledecode_hqx(data: ReadableBuffer, /) -> bytes: ...
    def rlecode_hqx(data: ReadableBuffer, /) -> bytes: ...
    def b2a_hqx(data: ReadableBuffer, /) -> bytes: ...

def crc_hqx(data: ReadableBuffer, crc: int, /) -> int: ...
def crc32(data: ReadableBuffer, crc: int = 0, /) -> int: ...
def b2a_hex(data: ReadableBuffer, sep: str | bytes = ..., bytes_per_sep: int = ...) -> bytes: ...
def hexlify(data: ReadableBuffer, sep: str | bytes = ..., bytes_per_sep: int = ...) -> bytes: ...
def a2b_hex(hexstr: _AsciiBuffer, /) -> bytes: ...
def unhexlify(hexstr: _AsciiBuffer, /) -> bytes: ...

class Error(ValueError): ...
class Incomplete(Exception): ...
