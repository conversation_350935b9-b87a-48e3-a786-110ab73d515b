"""
上传管理器 - 协调整个视频上传流程
"""

import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid


class UploadStatus(Enum):
    """上传状态枚举"""
    IDLE = "idle"
    PREPARING = "preparing"
    UPLOADING = "uploading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class UploadSession:
    """上传会话"""
    session_id: str
    file_paths: List[str]
    upload_config: Dict[str, Any]
    status: UploadStatus = UploadStatus.IDLE
    created_at: float = field(default_factory=time.time)
    started_at: float = 0.0
    completed_at: float = 0.0
    total_files: int = 0
    completed_files: int = 0
    failed_files: int = 0
    current_file: str = ""
    progress: float = 0.0
    error_message: str = ""
    metadata: Dict = field(default_factory=dict)


class UploadManager:
    """上传管理器"""
    
    def __init__(self, concurrency_manager, environment_manager, browser_manager, 
                 state_manager=None, logger=None):
        self.concurrency_manager = concurrency_manager
        self.environment_manager = environment_manager
        self.browser_manager = browser_manager
        self.state_manager = state_manager
        self.logger = logger
        
        # 上传会话管理
        self._sessions = {}  # session_id -> UploadSession
        self._current_session = None
        self._session_lock = threading.RLock()
        
        # 上传配置
        self._default_config = {
            'title_template': '自动上传视频 - {filename}',
            'description_template': '通过HubStudio自动上传',
            'tags': ['自动上传', 'HubStudio'],
            'privacy': 'private',
            'category': '22',  # People & Blogs
            'upload_timeout': 600,  # 10分钟超时
            'retry_count': 3,
            'retry_delay': 30,  # 重试延迟30秒
            'batch_size': 3,  # 批次大小
            'upload_interval': 5  # 上传间隔5秒
        }
        
        # 观察者
        self._observers = []
        
        # 统计信息
        self._stats = {
            'total_sessions': 0,
            'completed_sessions': 0,
            'failed_sessions': 0,
            'total_files_uploaded': 0,
            'total_upload_time': 0.0,
            'average_upload_time': 0.0
        }
        
        self._running = False
        
        self._log('info', '上传管理器初始化完成')
    
    def _log(self, level: str, message: str):
        """记录日志"""
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(f"[上传管理器] {message}")
    
    def add_observer(self, observer: Callable[[str, UploadSession], None]):
        """添加上传状态变更观察者"""
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer: Callable[[str, UploadSession], None]):
        """移除上传状态变更观察者"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self, event_type: str, session: UploadSession):
        """通知观察者"""
        for observer in self._observers:
            try:
                observer(event_type, session)
            except Exception as e:
                self._log('error', f'通知观察者失败: {e}')
    
    def start_upload(self, file_paths: List[str], upload_config: Dict[str, Any] = None) -> str:
        """开始上传"""
        try:
            if not file_paths:
                raise ValueError("文件列表不能为空")
            
            # 检查是否有正在进行的上传
            if self._current_session and self._current_session.status == UploadStatus.UPLOADING:
                raise RuntimeError("已有上传任务正在进行中")
            
            # 合并配置
            config = self._default_config.copy()
            if upload_config:
                config.update(upload_config)
            
            # 创建上传会话
            session_id = str(uuid.uuid4())
            session = UploadSession(
                session_id=session_id,
                file_paths=file_paths.copy(),
                upload_config=config,
                total_files=len(file_paths)
            )
            
            with self._session_lock:
                self._sessions[session_id] = session
                self._current_session = session
                self._stats['total_sessions'] += 1
            
            # 通知观察者
            self._notify_observers('upload_session_created', session)
            
            # 提交上传任务到并发管理器
            task_id = self.concurrency_manager.submit_task(
                self._execute_upload_session,
                session_id,
                priority=self.concurrency_manager.TaskPriority.HIGH,
                timeout=config.get('upload_timeout', 600),
                metadata={'session_id': session_id, 'type': 'upload_session'}
            )
            
            session.metadata['task_id'] = task_id
            
            self._log('info', f'上传会话已创建: {session_id}, 文件数: {len(file_paths)}')
            return session_id
            
        except Exception as e:
            self._log('error', f'开始上传失败: {e}')
            raise
    
    def pause_upload(self, session_id: str = None) -> bool:
        """暂停上传"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id) if session_id else self._current_session
                
                if not session:
                    return False
                
                if session.status != UploadStatus.UPLOADING:
                    return False
                
                session.status = UploadStatus.PAUSED
                
                # 取消相关任务
                task_id = session.metadata.get('task_id')
                if task_id:
                    self.concurrency_manager.cancel_task(task_id)
                
                # 通知观察者
                self._notify_observers('upload_session_paused', session)
                
                self._log('info', f'上传会话已暂停: {session.session_id}')
                return True
                
        except Exception as e:
            self._log('error', f'暂停上传失败: {e}')
            return False
    
    def resume_upload(self, session_id: str = None) -> bool:
        """恢复上传"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id) if session_id else self._current_session
                
                if not session:
                    return False
                
                if session.status != UploadStatus.PAUSED:
                    return False
                
                # 重新提交任务
                task_id = self.concurrency_manager.submit_task(
                    self._execute_upload_session,
                    session.session_id,
                    priority=self.concurrency_manager.TaskPriority.HIGH,
                    timeout=session.upload_config.get('upload_timeout', 600),
                    metadata={'session_id': session.session_id, 'type': 'upload_session'}
                )
                
                session.metadata['task_id'] = task_id
                session.status = UploadStatus.UPLOADING
                
                # 通知观察者
                self._notify_observers('upload_session_resumed', session)
                
                self._log('info', f'上传会话已恢复: {session.session_id}')
                return True
                
        except Exception as e:
            self._log('error', f'恢复上传失败: {e}')
            return False
    
    def cancel_upload(self, session_id: str = None) -> bool:
        """取消上传"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id) if session_id else self._current_session
                
                if not session:
                    return False
                
                if session.status in [UploadStatus.COMPLETED, UploadStatus.CANCELLED]:
                    return False
                
                session.status = UploadStatus.CANCELLED
                session.completed_at = time.time()
                
                # 取消相关任务
                task_id = session.metadata.get('task_id')
                if task_id:
                    self.concurrency_manager.cancel_task(task_id)
                
                # 通知观察者
                self._notify_observers('upload_session_cancelled', session)
                
                self._log('info', f'上传会话已取消: {session.session_id}')
                return True
                
        except Exception as e:
            self._log('error', f'取消上传失败: {e}')
            return False
    
    def _execute_upload_session(self, session_id: str):
        """执行上传会话"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id)
                if not session:
                    raise ValueError(f"上传会话不存在: {session_id}")
                
                session.status = UploadStatus.PREPARING
                session.started_at = time.time()
            
            # 通知观察者
            self._notify_observers('upload_session_started', session)
            
            self._log('info', f'开始执行上传会话: {session_id}')
            
            # 准备上传环境
            self._prepare_upload_environment(session)
            
            # 执行批量上传
            self._execute_batch_upload(session)
            
            # 完成上传会话
            self._complete_upload_session(session)
            
        except Exception as e:
            self._handle_upload_session_error(session_id, e)
    
    def _prepare_upload_environment(self, session: UploadSession):
        """准备上传环境"""
        try:
            self._log('info', f'准备上传环境: {session.session_id}')
            
            # 检查环境管理器状态
            running_envs = self.environment_manager.get_running_environments()
            if not running_envs:
                # 尝试启动一个环境
                all_envs = self.environment_manager.get_all_environments()
                if all_envs:
                    env_to_start = all_envs[0]
                    if not self.environment_manager.start_environment(env_to_start.environment_id):
                        raise RuntimeError("无法启动上传环境")
                else:
                    raise RuntimeError("没有可用的上传环境")
            
            # 更新会话状态
            session.status = UploadStatus.UPLOADING
            self._notify_observers('upload_session_environment_ready', session)
            
        except Exception as e:
            self._log('error', f'准备上传环境失败: {e}')
            raise
    
    def _execute_batch_upload(self, session: UploadSession):
        """执行批量上传"""
        try:
            batch_size = session.upload_config.get('batch_size', 3)
            upload_interval = session.upload_config.get('upload_interval', 5)
            
            # 分批处理文件
            file_batches = [session.file_paths[i:i + batch_size] 
                          for i in range(0, len(session.file_paths), batch_size)]
            
            for batch_index, batch_files in enumerate(file_batches):
                if session.status == UploadStatus.CANCELLED:
                    break
                
                self._log('info', f'处理批次 {batch_index + 1}/{len(file_batches)}: {len(batch_files)} 个文件')
                
                # 并发上传批次中的文件
                batch_tasks = []
                for file_path in batch_files:
                    if session.status == UploadStatus.CANCELLED:
                        break
                    
                    task_id = self.concurrency_manager.submit_task(
                        self._upload_single_file,
                        session.session_id,
                        file_path,
                        priority=self.concurrency_manager.TaskPriority.NORMAL,
                        timeout=session.upload_config.get('upload_timeout', 600),
                        metadata={
                            'session_id': session.session_id,
                            'file_path': file_path,
                            'type': 'single_file_upload'
                        }
                    )
                    batch_tasks.append(task_id)
                
                # 等待批次完成
                self._wait_for_batch_completion(session, batch_tasks)
                
                # 批次间隔
                if batch_index < len(file_batches) - 1 and session.status != UploadStatus.CANCELLED:
                    time.sleep(upload_interval)
            
        except Exception as e:
            self._log('error', f'批量上传执行失败: {e}')
            raise
    
    def _upload_single_file(self, session_id: str, file_path: str):
        """上传单个文件"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id)
                if not session or session.status == UploadStatus.CANCELLED:
                    return
                
                session.current_file = file_path
            
            self._log('info', f'开始上传文件: {file_path}')
            
            # 获取可用环境
            environment = self.environment_manager.get_available_environment()
            if not environment:
                raise RuntimeError("没有可用的上传环境")
            
            # 分配任务到环境
            self.environment_manager.assign_task_to_environment(environment.environment_id)
            
            try:
                # 获取浏览器资源
                browser_resource_id = self.concurrency_manager.get_available_browser_resource()
                if not browser_resource_id:
                    # 创建新的浏览器实例
                    browser_instance = self.browser_manager.create_browser_instance(environment.environment_id)
                    browser_resource_id = self.concurrency_manager.acquire_browser_resource(
                        environment.environment_id, browser_instance
                    )
                
                try:
                    # 执行实际的文件上传
                    self._perform_file_upload(session, file_path, browser_resource_id)
                    
                    # 更新成功统计
                    with self._session_lock:
                        session.completed_files += 1
                        session.progress = (session.completed_files / session.total_files) * 100
                    
                    self._notify_observers('file_upload_completed', session)
                    self._log('info', f'文件上传完成: {file_path}')
                    
                finally:
                    # 释放浏览器资源
                    self.concurrency_manager.mark_browser_resource_free(browser_resource_id)
                    
            finally:
                # 释放环境任务
                self.environment_manager.release_task_from_environment(environment.environment_id)
            
        except Exception as e:
            # 更新失败统计
            with self._session_lock:
                session = self._sessions.get(session_id)
                if session:
                    session.failed_files += 1
                    session.progress = ((session.completed_files + session.failed_files) / session.total_files) * 100
            
            self._notify_observers('file_upload_failed', session)
            self._log('error', f'文件上传失败 {file_path}: {e}')
            raise
    
    def _perform_file_upload(self, session: UploadSession, file_path: str, browser_resource_id: str):
        """执行实际的文件上传"""
        try:
            # 这里集成原有的上传逻辑
            # 1. 导航到YouTube上传页面
            # 2. 选择文件
            # 3. 填写视频信息
            # 4. 提交上传
            
            # 获取浏览器实例
            browser_resources = self.concurrency_manager.get_browser_resources_info()
            browser_resource = next((r for r in browser_resources if r['resource_id'] == browser_resource_id), None)
            
            if not browser_resource:
                raise RuntimeError("浏览器资源不可用")
            
            # 模拟上传过程（实际实现中需要集成原有的上传组件）
            upload_config = session.upload_config
            
            # 生成视频标题和描述
            import os
            filename = os.path.basename(file_path)
            title = upload_config.get('title_template', '').format(filename=filename) or filename
            description = upload_config.get('description_template', '')
            
            # 这里应该调用原有的上传组件
            # 例如：
            # from youtube_components.upload_orchestrator import UploadOrchestrator
            # orchestrator = UploadOrchestrator(browser_instance, self.logger)
            # result = orchestrator.upload_video(file_path, title, description, upload_config)
            
            # 模拟上传时间
            time.sleep(2)
            
            self._log('info', f'文件上传模拟完成: {file_path}')
            
        except Exception as e:
            self._log('error', f'执行文件上传失败: {e}')
            raise
    
    def _wait_for_batch_completion(self, session: UploadSession, task_ids: List[str]):
        """等待批次完成"""
        try:
            max_wait_time = 300  # 最大等待5分钟
            start_time = time.time()
            
            while task_ids and time.time() - start_time < max_wait_time:
                if session.status == UploadStatus.CANCELLED:
                    break
                
                # 检查任务状态
                completed_tasks = []
                for task_id in task_ids:
                    status = self.concurrency_manager.get_task_status(task_id)
                    if status in [self.concurrency_manager.TaskStatus.COMPLETED, 
                                self.concurrency_manager.TaskStatus.FAILED,
                                self.concurrency_manager.TaskStatus.CANCELLED]:
                        completed_tasks.append(task_id)
                
                # 移除已完成的任务
                for task_id in completed_tasks:
                    task_ids.remove(task_id)
                
                if not task_ids:
                    break
                
                time.sleep(1)
            
            # 取消剩余未完成的任务
            for task_id in task_ids:
                self.concurrency_manager.cancel_task(task_id)
            
        except Exception as e:
            self._log('error', f'等待批次完成失败: {e}')
    
    def _complete_upload_session(self, session: UploadSession):
        """完成上传会话"""
        try:
            with self._session_lock:
                session.status = UploadStatus.COMPLETED
                session.completed_at = time.time()
                
                # 更新统计信息
                self._stats['completed_sessions'] += 1
                self._stats['total_files_uploaded'] += session.completed_files
                
                upload_time = session.completed_at - session.started_at
                self._stats['total_upload_time'] += upload_time
                
                if self._stats['completed_sessions'] > 0:
                    self._stats['average_upload_time'] = (
                        self._stats['total_upload_time'] / self._stats['completed_sessions']
                    )
            
            # 通知观察者
            self._notify_observers('upload_session_completed', session)
            
            self._log('info', f'上传会话完成: {session.session_id}, '
                           f'成功: {session.completed_files}, 失败: {session.failed_files}')
            
        except Exception as e:
            self._log('error', f'完成上传会话失败: {e}')
    
    def _handle_upload_session_error(self, session_id: str, error: Exception):
        """处理上传会话错误"""
        try:
            with self._session_lock:
                session = self._sessions.get(session_id)
                if session:
                    session.status = UploadStatus.FAILED
                    session.error_message = str(error)
                    session.completed_at = time.time()
                    
                    self._stats['failed_sessions'] += 1
            
            # 通知观察者
            if session:
                self._notify_observers('upload_session_failed', session)
            
            self._log('error', f'上传会话失败: {session_id}, 错误: {error}')
            
        except Exception as e:
            self._log('error', f'处理上传会话错误失败: {e}')
    
    def get_session(self, session_id: str) -> Optional[UploadSession]:
        """获取上传会话"""
        with self._session_lock:
            return self._sessions.get(session_id)
    
    def get_current_session(self) -> Optional[UploadSession]:
        """获取当前上传会话"""
        with self._session_lock:
            return self._current_session
    
    def get_all_sessions(self) -> List[UploadSession]:
        """获取所有上传会话"""
        with self._session_lock:
            return list(self._sessions.values())
    
    def get_active_sessions(self) -> List[UploadSession]:
        """获取活动的上传会话"""
        with self._session_lock:
            return [s for s in self._sessions.values() 
                   if s.status in [UploadStatus.PREPARING, UploadStatus.UPLOADING]]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._session_lock:
            active_sessions = len(self.get_active_sessions())
            
            return {
                'total_sessions': self._stats['total_sessions'],
                'completed_sessions': self._stats['completed_sessions'],
                'failed_sessions': self._stats['failed_sessions'],
                'active_sessions': active_sessions,
                'total_files_uploaded': self._stats['total_files_uploaded'],
                'average_upload_time': round(self._stats['average_upload_time'], 2),
                'current_session_id': self._current_session.session_id if self._current_session else None
            }
    
    def update_default_config(self, config: Dict[str, Any]):
        """更新默认配置"""
        self._default_config.update(config)
        self._log('info', '默认上传配置已更新')
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return self._default_config.copy()
    
    def clear_completed_sessions(self, keep_recent_hours: int = 24) -> int:
        """清理已完成的会话"""
        cutoff_time = time.time() - (keep_recent_hours * 3600)
        removed_count = 0
        
        with self._session_lock:
            session_ids_to_remove = []
            for session_id, session in self._sessions.items():
                if (session.status in [UploadStatus.COMPLETED, UploadStatus.FAILED, UploadStatus.CANCELLED] and
                    session.completed_at < cutoff_time):
                    session_ids_to_remove.append(session_id)
            
            for session_id in session_ids_to_remove:
                del self._sessions[session_id]
                removed_count += 1
            
            # 如果当前会话被清理，重置当前会话
            if self._current_session and self._current_session.session_id in session_ids_to_remove:
                self._current_session = None
        
        self._log('info', f'已清理 {removed_count} 个已完成的上传会话')
        return removed_count
    
    def export_session_report(self, session_id: str = None) -> Dict[str, Any]:
        """导出会话报告"""
        with self._session_lock:
            session = self._sessions.get(session_id) if session_id else self._current_session
            
            if not session:
                return {}
            
            return {
                'session_id': session.session_id,
                'status': session.status.value,
                'created_at': session.created_at,
                'started_at': session.started_at,
                'completed_at': session.completed_at,
                'duration': session.completed_at - session.started_at if session.completed_at > 0 else 0,
                'total_files': session.total_files,
                'completed_files': session.completed_files,
                'failed_files': session.failed_files,
                'success_rate': (session.completed_files / session.total_files * 100) if session.total_files > 0 else 0,
                'progress': session.progress,
                'error_message': session.error_message,
                'upload_config': session.upload_config,
                'file_paths': session.file_paths
            }
    
    def __del__(self):
        """析构函数"""
        try:
            # 取消所有活动会话
            active_sessions = self.get_active_sessions()
            for session in active_sessions:
                self.cancel_upload(session.session_id)
        except Exception:
            pass