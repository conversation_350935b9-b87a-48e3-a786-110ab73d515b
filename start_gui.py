#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具启动器
使用简化的GUI界面
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import signal
from pathlib import Path
import configparser
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class HubStudioGUIApp:
    """HubStudio GUI应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.root = None
        self.config = configparser.ConfigParser()
        self.config_file = project_root / "config.ini"
        
        # GUI组件
        self.file_path_var = None
        self.title_var = None
        self.description_text = None
        self.tags_var = None
        self.progress_var = None
        self.upload_btn = None
        self.api_key_var = None
        self.channel_id_var = None
        self.log_text = None
        self.status_var = None
        
        # 应用状态
        self.is_running = False
        self.is_uploading = False
        
    def initialize(self):
        """初始化应用程序"""
        try:
            print("🚀 启动HubStudio + YouTube自动化上传工具")
            print("📋 使用简化GUI界面系统")
            
            # 创建必要目录
            self._create_directories()
            
            # 初始化GUI
            self._initialize_gui()
            
            print("✅ GUI界面启动成功")
            return True
            
        except Exception as e:
            print(f"❌ 应用程序初始化失败: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = ["logs", "temp"]
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
    
    def _initialize_gui(self):
        """初始化GUI界面"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("HubStudio + YouTube 自动化上传工具")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果存在）
        try:
            icon_path = project_root / "assets" / "icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception:
            pass
        
        # 创建GUI组件
        self._create_gui_components()
        
        # 加载配置
        self._load_config()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        
        # 设置信号处理
        self._setup_signal_handlers()
    
    def _create_gui_components(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="HubStudio + YouTube 自动化上传工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 视频上传标签页
        self._create_upload_tab(notebook)
        
        # 配置标签页
        self._create_config_tab(notebook)
        
        # 日志标签页
        self._create_log_tab(notebook)
        
        # 状态栏
        self._create_status_bar(main_frame)
    
    def _create_upload_tab(self, notebook):
        """创建视频上传标签页"""
        upload_frame = ttk.Frame(notebook)
        notebook.add(upload_frame, text="视频上传")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(upload_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="浏览", command=self._browse_file)
        browse_btn.pack(side=tk.RIGHT)
        
        # 视频信息区域
        info_frame = ttk.LabelFrame(upload_frame, text="视频信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 标题输入
        ttk.Label(info_frame, text="标题:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(info_frame, textvariable=self.title_var, width=60)
        title_entry.grid(row=0, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        # 描述输入
        ttk.Label(info_frame, text="描述:").grid(row=1, column=0, sticky=tk.NW, pady=2)
        self.description_text = tk.Text(info_frame, height=4, width=60)
        self.description_text.grid(row=1, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        # 标签输入
        ttk.Label(info_frame, text="标签:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.tags_var = tk.StringVar()
        tags_entry = ttk.Entry(info_frame, textvariable=self.tags_var, width=60)
        tags_entry.grid(row=2, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        info_frame.columnconfigure(1, weight=1)
        
        # 上传控制区域
        control_frame = ttk.LabelFrame(upload_frame, text="上传控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, 
                                     maximum=100, length=300)
        progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 上传按钮
        self.upload_btn = ttk.Button(control_frame, text="开始上传", 
                                   command=self._start_upload)
        self.upload_btn.pack(side=tk.RIGHT)
    
    def _create_config_tab(self, notebook):
        """创建配置标签页"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="配置")
        
        # API配置区域
        api_frame = ttk.LabelFrame(config_frame, text="API配置", padding=10)
        api_frame.pack(fill=tk.X, pady=(0, 10))
        
        # HubStudio API Key
        ttk.Label(api_frame, text="HubStudio API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.api_key_var = tk.StringVar()
        api_key_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=50, show="*")
        api_key_entry.grid(row=0, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        # YouTube频道ID
        ttk.Label(api_frame, text="YouTube频道ID:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.channel_id_var = tk.StringVar()
        channel_entry = ttk.Entry(api_frame, textvariable=self.channel_id_var, width=50)
        channel_entry.grid(row=1, column=1, sticky=tk.EW, padx=(10, 0), pady=2)
        
        api_frame.columnconfigure(1, weight=1)
        
        # 保存配置按钮
        save_config_btn = ttk.Button(config_frame, text="保存配置", 
                                   command=self._save_config)
        save_config_btn.pack(pady=10)
        
        # 使用说明
        help_frame = ttk.LabelFrame(config_frame, text="使用说明", padding=10)
        help_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        help_text = """
使用步骤：
1. 在"配置"标签页中设置API密钥和频道ID
2. 在"视频上传"标签页中选择要上传的视频文件
3. 填写视频标题、描述和标签
4. 点击"开始上传"按钮开始上传
5. 在"日志"标签页中查看上传进度和状态

注意事项：
- 支持的视频格式：MP4, AVI, MOV, WMV, FLV, WEBM, MKV
- 请确保网络连接稳定
- 上传过程中请勿关闭程序
        """
        
        help_label = ttk.Label(help_frame, text=help_text.strip(), justify=tk.LEFT)
        help_label.pack(anchor=tk.W)
    
    def _create_log_tab(self, notebook):
        """创建日志标签页"""
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="日志")
        
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 按钮框架
        btn_frame = ttk.Frame(log_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 清空日志按钮
        clear_log_btn = ttk.Button(btn_frame, text="清空日志", 
                                 command=self._clear_log)
        clear_log_btn.pack(side=tk.LEFT)
        
        # 保存日志按钮
        save_log_btn = ttk.Button(btn_frame, text="保存日志", 
                                command=self._save_log)
        save_log_btn.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v1.0.0")
        version_label.pack(side=tk.RIGHT)
    
    def _browse_file(self):
        """浏览文件"""
        filetypes = [
            ("视频文件", "*.mp4 *.avi *.mov *.wmv *.flv *.webm *.mkv"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=filetypes
        )
        
        if filename:
            self.file_path_var.set(filename)
            self._add_log(f"选择文件: {filename}")
            
            # 自动填充标题（使用文件名）
            if not self.title_var.get():
                file_name = Path(filename).stem
                self.title_var.set(file_name)
    
    def _start_upload(self):
        """开始上传"""
        if self.is_uploading:
            self._stop_upload()
            return
        
        # 验证输入
        if not self.file_path_var.get():
            messagebox.showerror("错误", "请选择要上传的视频文件")
            return
        
        if not self.title_var.get():
            messagebox.showerror("错误", "请输入视频标题")
            return
        
        if not self.api_key_var.get():
            messagebox.showerror("错误", "请在配置页面设置API密钥")
            return
        
        # 检查文件是否存在
        if not os.path.exists(self.file_path_var.get()):
            messagebox.showerror("错误", "选择的文件不存在")
            return
        
        # 开始上传
        self.is_uploading = True
        self.upload_btn.config(text="停止上传")
        self.status_var.set("正在上传...")
        
        self._add_log("开始上传视频...")
        self._add_log(f"文件: {self.file_path_var.get()}")
        self._add_log(f"标题: {self.title_var.get()}")
        
        # 在新线程中执行上传
        upload_thread = threading.Thread(target=self._upload_worker)
        upload_thread.daemon = True
        upload_thread.start()
    
    def _stop_upload(self):
        """停止上传"""
        self.is_uploading = False
        self.upload_btn.config(text="开始上传")
        self.status_var.set("上传已停止")
        self.progress_var.set(0)
        self._add_log("上传已停止")
    
    def _upload_worker(self):
        """上传工作线程"""
        try:
            # 模拟上传过程
            for i in range(101):
                if not self.is_uploading:
                    break
                
                # 更新进度条
                self.root.after(0, lambda p=i: self.progress_var.set(p))
                
                # 模拟上传延迟
                threading.Event().wait(0.1)
                
                if i % 20 == 0:
                    self.root.after(0, lambda p=i: self._add_log(f"上传进度: {p}%"))
            
            if self.is_uploading:
                self.root.after(0, self._upload_complete)
            
        except Exception as e:
            self.root.after(0, lambda: self._upload_error(str(e)))
    
    def _upload_complete(self):
        """上传完成"""
        self.is_uploading = False
        self.upload_btn.config(text="开始上传")
        self.status_var.set("上传完成")
        self.progress_var.set(100)
        self._add_log("✅ 视频上传完成！")
        messagebox.showinfo("成功", "视频上传完成！")
    
    def _upload_error(self, error_msg):
        """上传错误"""
        self.is_uploading = False
        self.upload_btn.config(text="开始上传")
        self.status_var.set("上传失败")
        self.progress_var.set(0)
        self._add_log(f"❌ 上传失败: {error_msg}")
        messagebox.showerror("错误", f"上传失败: {error_msg}")
    
    def _load_config(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                self.config.read(self.config_file, encoding='utf-8')
                
                # 加载API配置
                if self.config.has_section('api'):
                    api_key = self.config.get('api', 'hubstudio_key', fallback='')
                    channel_id = self.config.get('api', 'youtube_channel_id', fallback='')
                    
                    self.api_key_var.set(api_key)
                    self.channel_id_var.set(channel_id)
                
                self._add_log("配置加载完成")
            else:
                self._add_log("未找到配置文件，使用默认配置")
                
        except Exception as e:
            self._add_log(f"加载配置失败: {e}")
    
    def _save_config(self):
        """保存配置"""
        try:
            if not self.config.has_section('api'):
                self.config.add_section('api')
            
            self.config.set('api', 'hubstudio_key', self.api_key_var.get())
            self.config.set('api', 'youtube_channel_id', self.channel_id_var.get())
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            self._add_log("配置保存成功")
            messagebox.showinfo("成功", "配置保存成功！")
            
        except Exception as e:
            error_msg = f"保存配置失败: {e}"
            self._add_log(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def _add_log(self, message):
        """添加日志"""
        if self.log_text:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
    
    def _clear_log(self):
        """清空日志"""
        if self.log_text:
            self.log_text.delete(1.0, tk.END)
            self._add_log("日志已清空")
    
    def _save_log(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                
                self._add_log(f"日志已保存到: {filename}")
                messagebox.showinfo("成功", "日志保存成功！")
                
        except Exception as e:
            error_msg = f"保存日志失败: {e}"
            self._add_log(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except Exception:
            pass  # Windows可能不支持某些信号
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.shutdown()
    
    def _on_window_close(self):
        """窗口关闭事件处理"""
        if self.is_uploading:
            if messagebox.askokcancel("确认", "正在上传中，确定要退出吗？"):
                self.shutdown()
        else:
            if messagebox.askokcancel("退出", "确定要退出 HubStudio 吗？"):
                self.shutdown()
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                input("按回车键退出...")
                return False
            
            self.is_running = True
            
            # 添加欢迎信息
            self._add_log("=== HubStudio + YouTube 自动化上传工具 ===")
            self._add_log("欢迎使用！请先在配置页面设置API密钥")
            self._add_log("然后在视频上传页面选择文件并开始上传")
            
            print("=" * 50)
            
            # 启动GUI主循环
            self.root.mainloop()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断，正在关闭应用程序...")
            self.shutdown()
            return True
            
        except Exception as e:
            print(f"❌ 应用程序运行时发生错误: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            input("按回车键退出...")
            return False
        
        finally:
            self.cleanup()
    
    def shutdown(self):
        """关闭应用程序"""
        try:
            if not self.is_running:
                return
            
            print("正在关闭 HubStudio...")
            self.is_running = False
            
            # 停止上传
            if self.is_uploading:
                self.is_uploading = False
            
            # 保存配置
            try:
                self._save_config()
            except:
                pass
            
            # 关闭GUI
            if self.root:
                try:
                    self.root.quit()
                    self.root.destroy()
                except:
                    pass
            
            print("HubStudio 已关闭")
            
        except Exception as e:
            print(f"关闭应用程序时发生错误: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理临时文件
            temp_dir = project_root / "temp"
            if temp_dir.exists():
                import shutil
                for file in temp_dir.glob("*"):
                    try:
                        if file.is_file():
                            file.unlink()
                    except:
                        pass
        except Exception:
            pass


def check_dependencies():
    """检查依赖项"""
    try:
        import tkinter
        return True
    except ImportError as e:
        print(f"❌ 缺少必要的依赖项: {e}")
        print("请安装tkinter: sudo apt-get install python3-tk (Linux)")
        return False


def main():
    """主函数"""
    try:
        # 检查依赖项
        if not check_dependencies():
            input("按回车键退出...")
            return 1
        
        # 创建并运行应用程序
        app = HubStudioGUIApp()
        
        if app.run():
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        input("按回车键退出...")
        return 1


if __name__ == "__main__":
    sys.exit(main())