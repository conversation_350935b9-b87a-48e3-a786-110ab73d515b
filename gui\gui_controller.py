"""
GUI控制器 - 处理业务逻辑和数据管理
分离界面展示和业务逻辑
"""

import threading
import os
import configparser
import queue
import time
from datetime import datetime
import concurrent.futures
from pathlib import Path

# 导入自动化模块
try:
    from youtube_components import HubStudioManager
except ImportError:
    # 如果没有youtube_components模块，创建一个简单的替代
    class HubStudioManager:
        def test_api_connection(self, api_url, api_id, api_secret):
            return {'success': True, 'message': '连接测试成功（模拟）'}
        
        def get_environment_list(self, api_url, api_id, api_secret):
            return {
                'success': True, 
                'data': [
                    {'containerCode': 'env001', 'containerName': '测试环境1'},
                    {'containerCode': 'env002', 'containerName': '测试环境2'}
                ]
            }

# 简化的自动化类
class SimpleHubStudioAutomation:
    """简化的HubStudio自动化类"""
    
    def __init__(self):
        self.config = configparser.ConfigParser()
        self.initialized = False
    
    def initialize(self, profile_id=None):
        """初始化自动化系统"""
        try:
            # 模拟初始化过程
            time.sleep(1)
            self.initialized = True
            return True
        except Exception as e:
            return False
    
    def upload_single_video(self, video_path, title, description, children_content=None):
        """上传单个视频（模拟）"""
        try:
            # 模拟上传过程
            time.sleep(2)
            # 随机成功/失败（80%成功率）
            import random
            return random.random() > 0.2
        except Exception as e:
            return False
    
    def cleanup(self):
        """清理资源"""
        self.initialized = False


class GUIController:
    """GUI控制器类 - 负责业务逻辑处理"""
    
    def __init__(self):
        # 初始化配置
        self.config = configparser.ConfigParser()
        self.load_config()
        
        # 初始化状态变量
        self.automation = None
        self.video_list = []
        self.is_uploading = False
        self.log_queue = queue.Queue()
        self.upload_results = {}
        
        # 回调函数（由GUI视图设置）
        self.callbacks = {
            'on_status_update': None,
            'on_progress_update': None,
            'on_log_message': None,
            'on_env_list_update': None,
            'on_video_count_update': None,
            'on_upload_finished': None
        }
    
    def set_callback(self, event_name, callback_func):
        """设置回调函数"""
        if event_name in self.callbacks:
            self.callbacks[event_name] = callback_func
    
    def _trigger_callback(self, event_name, *args, **kwargs):
        """触发回调函数"""
        callback = self.callbacks.get(event_name)
        if callback:
            callback(*args, **kwargs)
    
    # ==================== 配置管理 ====================
    
    def load_config(self):
        """加载配置文件"""
        try:
            # 处理PyInstaller打包后的路径问题
            import sys
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件
                base_path = sys._MEIPASS
                config_path = os.path.join(base_path, 'config.ini')
            else:
                # 如果是开发环境
                config_path = 'config.ini'
                
            if os.path.exists(config_path):
                self.config.read(config_path, encoding='utf-8')
                self._ensure_config_completeness()
            else:
                self.create_default_config()
        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}", "ERROR")
    
    def _ensure_config_completeness(self):
        """确保配置完整性"""
        config_updated = False
        
        sections = {
            'BROWSER': {
                'api_url': 'http://localhost:6873',
                'readonly_mode': 'false',
                'headless_mode': 'false',
                'cdp_hide': 'true'
            },
            'HUBSTUDIO': {
                'api_id': '',
                'api_secret': ''
            },
            'VIDEO': {
                'video_folder': './videos',
                'default_title': '自动上传视频',
                'default_description': '通过HubStudio自动化脚本上传到YouTube的视频',
                'default_tags': '自动化,HubStudio,YouTube,视频上传'
            },
            'AUTOMATION': {
                'wait_timeout': '30',
                'upload_timeout': '600',
                'retry_count': '3',
                'upload_interval': '10'
            },
            'YOUTUBE': {
                'upload_url': 'https://studio.youtube.com'
            }
        }
        
        for section_name, section_data in sections.items():
            if not self.config.has_section(section_name):
                self.config.add_section(section_name)
                config_updated = True
            
            for key, default_value in section_data.items():
                if not self.config.has_option(section_name, key):
                    self.config.set(section_name, key, default_value)
                    config_updated = True
        
        if config_updated:
            self.save_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['BROWSER'] = {
            'api_url': 'http://localhost:6873',
            'readonly_mode': 'false',
            'headless_mode': 'false',
            'cdp_hide': 'true'
        }
        self.config['HUBSTUDIO'] = {
            'api_id': '',
            'api_secret': ''
        }
        self.config['YOUTUBE'] = {
            'upload_url': 'https://studio.youtube.com'
        }
        self.config['VIDEO'] = {
            'video_folder': './videos',
            'default_title': '自动上传视频',
            'default_description': '通过HubStudio自动化脚本上传到YouTube的视频',
            'default_tags': '自动化,HubStudio,YouTube,视频上传'
        }
        self.config['AUTOMATION'] = {
            'wait_timeout': '30',
            'upload_timeout': '600',
            'retry_count': '3',
            'upload_interval': '10'
        }
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 处理PyInstaller打包后的路径问题
            import sys
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件，保存到exe同目录
                exe_dir = os.path.dirname(sys.executable)
                config_path = os.path.join(exe_dir, 'config.ini')
            else:
                # 如果是开发环境
                config_path = 'config.ini'
                
            with open(config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)
            return True
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}", "ERROR")
            return False
    
    def update_config(self, section, key, value):
        """更新配置项"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, value)
    
    def get_config(self, section, key, fallback=''):
        """获取配置项"""
        return self.config.get(section, key, fallback=fallback)
    
    # ==================== 日志管理 ====================
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.log_queue.put(formatted_message)
        self._trigger_callback('on_log_message', formatted_message)
    
    def get_log_messages(self):
        """获取日志消息"""
        messages = []
        try:
            while True:
                message = self.log_queue.get_nowait()
                messages.append(message)
        except queue.Empty:
            pass
        return messages
    
    def clear_logs(self):
        """清空日志"""
        # 清空队列
        while not self.log_queue.empty():
            try:
                self.log_queue.get_nowait()
            except queue.Empty:
                break
    
    # ==================== 视频文件管理 ====================
    
    def add_video_file(self, file_path):
        """添加单个视频文件"""
        if file_path and os.path.exists(file_path):
            if file_path not in self.video_list:
                self.video_list.append(file_path)
                self.log_message(f"✅ 添加视频文件: {os.path.basename(file_path)}")
                self._trigger_callback('on_video_count_update', len(self.video_list), file_path)
            else:
                self.log_message(f"⚠️ 视频文件已存在: {os.path.basename(file_path)}", "WARNING")
        else:
            self.log_message(f"❌ 无效的文件路径: {file_path}", "ERROR")
    
    def add_video_folder(self, folder_path):
        """添加视频文件夹中的所有视频文件"""
        if not folder_path or not os.path.exists(folder_path):
            self.log_message(f"❌ 无效的文件夹路径: {folder_path}", "ERROR")
            return
        
        if not os.path.isdir(folder_path):
            self.log_message(f"❌ 路径不是文件夹: {folder_path}", "ERROR")
            return
        
        # 支持的视频格式
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.3g2', '.mts', '.m2ts', '.ts']
        added_count = 0
        
        try:
            self.log_message(f"📁 开始扫描文件夹: {folder_path}")
            
            # 递归扫描文件夹
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()
                    
                    # 检查是否为视频文件
                    if file_ext in video_extensions:
                        try:
                            # 检查文件大小（至少1MB）
                            file_size = os.path.getsize(file_path)
                            if file_size >= 1024 * 1024:  # 1MB
                                if file_path not in self.video_list:
                                    self.video_list.append(file_path)
                                    added_count += 1
                                    self.log_message(f"  ✅ 添加: {os.path.basename(file)} ({file_size / (1024*1024):.1f}MB)")
                                else:
                                    self.log_message(f"  ⚠️ 已存在: {os.path.basename(file)}")
                            else:
                                self.log_message(f"  ⚠️ 文件太小，跳过: {os.path.basename(file)} ({file_size}字节)")
                        except OSError as e:
                            self.log_message(f"  ❌ 无法访问文件: {os.path.basename(file)} - {e}", "ERROR")
            
            if added_count > 0:
                self.log_message(f"📁 文件夹扫描完成，新增 {added_count} 个视频文件")
                self.log_message(f"📊 当前总计 {len(self.video_list)} 个视频文件")
                self._trigger_callback('on_video_count_update', len(self.video_list), folder_path)
            else:
                self.log_message(f"📁 文件夹扫描完成，未找到新的视频文件")
                self.log_message(f"💡 支持的视频格式: {', '.join(video_extensions)}")
                
        except Exception as e:
            self.log_message(f"❌ 扫描文件夹时出错: {e}", "ERROR")
    
    def clear_video_selection(self):
        """清除视频选择"""
        self.video_list = []
        self.log_message("已清除视频选择", "INFO")
        self._trigger_callback('on_video_count_update', 0, "")
    
    def get_video_list(self):
        """获取视频列表"""
        return self.video_list.copy()
    
    def get_video_count(self):
        """获取视频数量"""
        return len(self.video_list)
    
    # ==================== HubStudio环境管理 ====================
    
    def test_hubstudio_connection(self, api_url, api_id, api_secret):
        """测试HubStudio连接 - 使用新的HubStudioManager组件"""
        def test_in_thread():
            try:
                self.log_message("正在测试HubStudio连接...", "INFO")
                
                if not api_id or not api_secret:
                    self.log_message("❌ API凭证未配置，请先配置API ID和API Secret", "ERROR")
                    return
                
                # 创建HubStudio管理器
                hubstudio_manager = HubStudioManager()
                
                # 测试API连接
                result = hubstudio_manager.test_api_connection(api_url, api_id, api_secret)
                
                if result['success']:
                    self.log_message(f"✅ HubStudio API连接成功: {result['message']}", "INFO")
                    # 自动刷新环境列表
                    self.refresh_environments(api_url, api_id, api_secret)
                else:
                    self.log_message(f"❌ HubStudio API连接失败: {result['message']}", "ERROR")
                    
            except Exception as e:
                self.log_message(f"❌ 连接测试异常: {e}", "ERROR")
        
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def refresh_environments(self, api_url, api_id, api_secret):
        """刷新HubStudio环境列表 - 使用新的HubStudioManager组件"""
        def refresh_in_thread():
            try:
                self.log_message("正在获取HubStudio环境列表...", "INFO")
                
                if not api_id or not api_secret:
                    self._trigger_callback('on_env_list_update', [], "请配置API凭证")
                    return
                
                # 创建HubStudio管理器
                hubstudio_manager = HubStudioManager()
                
                # 获取环境列表
                env_result = hubstudio_manager.get_environment_list(api_url, api_id, api_secret)
                
                if env_result['success']:
                    raw_env_list = env_result['data']
                    
                    # 转换环境数据为显示格式
                    env_list = []
                    available_count = 0
                    
                    for env in raw_env_list:
                        if isinstance(env, dict):
                            container_code = env.get('containerCode', '')
                            container_name = env.get('containerName', '')
                            
                            # 简化状态判断 - 假设所有环境都是可用的（已关闭状态）
                            status_text = "已关闭"
                            available_count += 1
                            
                            env_display = f"{container_code} - {container_name} - {status_text}"
                            env_list.append(env_display)
                    
                    if env_list:
                        status_msg = f"发现 {len(env_list)} 个环境 (可用: {available_count})"
                        self.log_message(f"发现 {len(env_list)} 个HubStudio环境，其中 {available_count} 个可用", "INFO")
                    else:
                        status_msg = "未发现环境"
                        self.log_message("未发现任何HubStudio环境", "WARNING")
                    
                    self._trigger_callback('on_env_list_update', env_list, status_msg)
                else:
                    self.log_message(f"获取环境列表失败: {env_result['message']}", "ERROR")
                    self._trigger_callback('on_env_list_update', [], "获取失败")
                
            except Exception as e:
                self.log_message(f"获取环境列表异常: {e}", "ERROR")
                self._trigger_callback('on_env_list_update', [], "连接异常")
        
        threading.Thread(target=refresh_in_thread, daemon=True).start()
    
    # 注意：_fetch_environment_list 方法已被移除，现在使用 HubStudioManager 组件
    
    # 注意：_fetch_environment_status 方法已被移除，现在使用 HubStudioManager 组件
    
    def get_available_environments(self, api_url, api_id, api_secret):
        """获取可用环境列表 - 使用新的HubStudioManager组件"""
        try:
            # 创建HubStudio管理器
            hubstudio_manager = HubStudioManager()
            
            # 获取环境列表
            env_result = hubstudio_manager.get_environment_list(api_url, api_id, api_secret)
            
            if env_result['success']:
                raw_env_list = env_result['data']
                
                # 转换环境数据为显示格式，只返回可用环境
                available_envs = []
                for env in raw_env_list:
                    if isinstance(env, dict):
                        container_code = env.get('containerCode', '')
                        container_name = env.get('containerName', '')
                        
                        # 简化状态判断 - 假设所有环境都是可用的（已关闭状态）
                        status_text = "已关闭"
                        env_display = f"{container_code} - {container_name} - {status_text}"
                        available_envs.append(env_display)
                
                return available_envs
            else:
                self.log_message(f"获取可用环境失败: {env_result['message']}", "ERROR")
                return []
        except Exception as e:
            self.log_message(f"获取可用环境异常: {e}", "ERROR")
            return []
    
    # ==================== 视频上传管理 ====================
    
    def start_upload(self, api_url, api_id, api_secret, concurrent_count, title, description, children_content):
        """开始上传视频 - 使用HubStudio自动化类"""
        if self.is_uploading:
            self.log_message("上传正在进行中...", "WARNING")
            return False
        
        if not self.video_list:
            self.log_message("请先添加视频文件", "ERROR")
            return False
        
        # 开始上传
        self.is_uploading = True
        self._trigger_callback('on_status_update', "🚀 正在初始化视频上传...")
        
        # 在新线程中执行上传
        upload_thread = threading.Thread(
            target=self._upload_videos_thread,
            args=(api_url, api_id, api_secret, concurrent_count, title, description, children_content),
            daemon=True
        )
        upload_thread.start()
        
        return True
    
    def stop_upload(self):
        """停止上传"""
        if self.is_uploading:
            self.is_uploading = False
            self.log_message("正在停止上传任务...", "INFO")
            self._trigger_callback('on_status_update', "⏹️ 正在停止上传...")
    
    def _upload_videos_thread(self, api_url, api_id, api_secret, concurrent_count, title_template, description_template, children_content):
        """上传视频的线程函数 - 使用HubStudio自动化类"""
        try:
            video_count = len(self.video_list)
            self.log_message(f"📊 开始上传 {video_count} 个视频文件")
            
            # 初始化上传结果
            self.upload_results = {
                'total': video_count,
                'success': 0,
                'failed': 0,
                'current': 0,
                'details': []
            }
            
            # 创建自动化实例
            automation = SimpleHubStudioAutomation()
            
            # 更新配置
            automation.config.set('HUBSTUDIO', 'api_url', api_url)
            automation.config.set('HUBSTUDIO', 'api_id', api_id)
            automation.config.set('HUBSTUDIO', 'api_secret', api_secret)
            automation.config.set('BROWSER', 'api_url', api_url)
            
            # 初始化自动化实例
            if not automation.initialize():
                self.log_message("❌ HubStudio自动化初始化失败", "ERROR")
                self._finish_upload(False)
                return
            
            self.log_message("✅ HubStudio自动化初始化成功")
            
            # 逐个上传视频
            for i, video_path in enumerate(self.video_list):
                if not self.is_uploading:
                    self.log_message("⏹️ 用户停止了上传", "INFO")
                    break
                
                self.upload_results['current'] = i + 1
                video_name = os.path.basename(video_path)
                
                self.log_message(f"📹 正在上传视频 {i+1}/{video_count}: {video_name}")
                self._trigger_callback('on_status_update', f"正在上传: {video_name}")
                
                # 计算进度
                progress = int((i / video_count) * 100)
                self._trigger_callback('on_progress_update', progress)
                
                try:
                    # 准备视频信息
                    title = title_template.replace('{filename}', os.path.splitext(video_name)[0]) if title_template else video_name
                    description = description_template if description_template else "通过HubStudio自动化上传"
                    
                    # 上传视频
                    success = automation.upload_single_video(
                        video_path=video_path,
                        title=title,
                        description=description,
                        children_content=children_content
                    )
                    
                    if success:
                        self.upload_results['success'] += 1
                        self.log_message(f"✅ 视频上传成功: {video_name}")
                        self.upload_results['details'].append({
                            'file': video_name,
                            'status': 'success',
                            'message': '上传成功'
                        })
                    else:
                        self.upload_results['failed'] += 1
                        self.log_message(f"❌ 视频上传失败: {video_name}", "ERROR")
                        self.upload_results['details'].append({
                            'file': video_name,
                            'status': 'failed',
                            'message': '上传失败'
                        })
                        
                except Exception as e:
                    self.upload_results['failed'] += 1
                    error_msg = str(e)
                    self.log_message(f"❌ 视频上传异常: {video_name} - {error_msg}", "ERROR")
                    self.upload_results['details'].append({
                        'file': video_name,
                        'status': 'error',
                        'message': error_msg
                    })
                
                # 上传间隔（除了最后一个视频）
                if i < video_count - 1 and self.is_uploading:
                    interval = int(self.get_config('AUTOMATION', 'upload_interval', '10'))
                    self.log_message(f"⏱️ 等待 {interval} 秒后上传下一个视频...")
                    time.sleep(interval)
            
            # 清理资源
            try:
                automation.cleanup()
            except:
                pass
            
            # 完成上传
            success_count = self.upload_results['success']
            total_count = self.upload_results['total']
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
            
            self.log_message(f"\n📊 上传完成统计:")
            self.log_message(f"总数: {total_count}, 成功: {success_count}, 失败: {self.upload_results['failed']}")
            self.log_message(f"成功率: {success_rate:.1f}%")
            
            overall_success = success_count > 0
            self._finish_upload(overall_success)
            
        except Exception as e:
            self.log_message(f"❌ 上传线程异常: {e}", "ERROR")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}", "ERROR")
            self._finish_upload(False)
    
    def _finish_upload(self, success):
        """完成上传处理"""
        self.is_uploading = False
        self._trigger_callback('on_progress_update', 100)
        
        if success:
            self._trigger_callback('on_status_update', "✅ 上传完成")
        else:
            self._trigger_callback('on_status_update', "❌ 上传失败")
        
        self._trigger_callback('on_upload_finished', success)
    
    def _batch_concurrent_upload_thread(self, all_envs, concurrent_count, title_template, description_template, children_content):
        """分批并发上传视频的线程函数"""
        try:
            total_envs = len(all_envs)
            video_count = len(self.video_list)
            
            self.log_message(f"分批并发上传模式", "INFO")
            self.log_message(f"总环境数量: {total_envs} 个", "INFO")
            self.log_message(f"每批并发数量: {concurrent_count} 个", "INFO")
            self.log_message(f"视频文件数量: {video_count} 个", "INFO")
            
            # 计算总批次数
            total_batches = (total_envs + concurrent_count - 1) // concurrent_count
            self.log_message(f"总批次数: {total_batches} 批", "INFO")
            
            # 创建结果统计
            self.upload_results = {
                'total': total_envs,  # 总数是环境数量
                'completed': 0,
                'success': 0,
                'failed': 0,
                'lock': threading.Lock()
            }
            
            # 分批处理环境
            for batch_index in range(total_batches):
                if not self.is_uploading:
                    self.log_message("检测到停止信号，终止分批上传", "INFO")
                    break
                
                # 计算当前批次的环境范围
                start_idx = batch_index * concurrent_count
                end_idx = min(start_idx + concurrent_count, total_envs)
                current_batch_envs = all_envs[start_idx:end_idx]
                current_batch_size = len(current_batch_envs)
                
                self.log_message(f"\n开始第 {batch_index + 1}/{total_batches} 批次上传", "INFO")
                self.log_message(f"当前批次环境: {[env.split(' - ')[0] for env in current_batch_envs]}", "INFO")
                
                # 记录批次开始前的完成数量
                batch_start_completed = self.upload_results['completed']
                
                # 更新状态
                self._trigger_callback('on_status_update', 
                    f"批次 {batch_index + 1}/{total_batches} - 处理 {current_batch_size} 个环境")
                
                # 当前批次的并发上传
                if video_count == 1:
                    # 单个视频：当前批次的所有环境上传同一个视频
                    video_path = self.video_list[0]
                    self.log_message(f"单视频模式：当前批次 {current_batch_size} 个环境上传: {os.path.basename(video_path)}", "INFO")
                    
                    with concurrent.futures.ThreadPoolExecutor(max_workers=current_batch_size) as executor:
                        futures = []
                        for i, env in enumerate(current_batch_envs):
                            worker_id = start_idx + i + 1
                            future = executor.submit(
                                self._single_env_upload_worker,
                                env, video_path, title_template, description_template, children_content, worker_id
                            )
                            futures.append(future)
                        
                        # 等待当前批次完成
                        concurrent.futures.wait(futures)
                        
                        # 检查当前批次是否有任何环境成功启动
                        batch_completed = self.upload_results['completed'] - batch_start_completed
                        if batch_completed == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                        
                        # 检查当前批次是否有任何环境成功启动
                        batch_completed = self.upload_results['completed'] - batch_start_completed
                        if batch_completed == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                else:
                    # 多个视频：当前批次的环境处理视频队列
                    self.log_message(f"多视频模式：当前批次 {current_batch_size} 个环境处理视频队列", "INFO")
                    
                    # 创建当前批次的视频队列
                    batch_video_queue = queue.Queue()
                    for i, video_path in enumerate(self.video_list):
                        batch_video_queue.put((i, video_path))
                    
                    with concurrent.futures.ThreadPoolExecutor(max_workers=current_batch_size) as executor:
                        futures = []
                        for i, env in enumerate(current_batch_envs):
                            worker_id = start_idx + i + 1
                            future = executor.submit(
                                self._multi_video_upload_worker,
                                env, batch_video_queue, title_template, description_template, children_content, worker_id
                            )
                            futures.append(future)
                        
                        # 等待当前批次完成
                        concurrent.futures.wait(futures)
                        
                        # 检查是否有任何环境成功启动
                        if self.upload_results['completed'] == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                
                # 批次间隔（可配置）
                if batch_index < total_batches - 1 and self.is_uploading:
                    batch_interval = int(self.get_config('AUTOMATION', 'batch_interval', '10'))
                    self.log_message(f"批次 {batch_index + 1} 完成，等待 {batch_interval} 秒后开始下一批次", "INFO")
                    time.sleep(batch_interval)
            
            # 完成上传
            self._trigger_callback('on_progress_update', 100)
            total_tasks = self.upload_results['total']
            success_count = self.upload_results['success']
            failed_count = self.upload_results['failed']
            success_rate = (success_count / total_tasks) * 100 if total_tasks > 0 else 0
            
            self.log_message(f"\n分批并发上传完成！", "INFO")
            self.log_message(f"总环境数: {total_tasks}, 成功: {success_count}, 失败: {failed_count}", "INFO")
            self.log_message(f"成功率: {success_rate:.1f}%", "INFO")
            
            # 修复成功判断逻辑：只有当成功率大于50%时才认为整体成功
            overall_success = success_count > 0 and success_rate >= 50.0
            if success_count == 0:
                self.log_message("❌ 所有上传任务都失败了，请检查网络连接和YouTube登录状态", "ERROR")
            elif success_rate < 50.0:
                self.log_message(f"⚠️ 成功率较低({success_rate:.1f}%)，建议检查环境配置", "WARNING")
            else:
                self.log_message(f"✅ 上传任务完成，成功率: {success_rate:.1f}%", "INFO")
                
            self._trigger_callback('on_upload_finished', overall_success)
            
        except Exception as e:
            error_msg = f"分批并发上传过程中发生错误: {e}"
            self.log_message(error_msg, "ERROR")
            self._trigger_callback('on_upload_finished', False)
    
    def _single_env_upload_worker(self, env, video_path, title_template, description_template, children_content, worker_id):
        """单个环境上传视频的工作线程（单视频模式）"""
        automation = None
        try:
            if not self.is_uploading:
                self.log_message(f"工作线程 {worker_id} 检测到上传已停止，退出", "INFO")
                return
            
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"🚀 工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            automation = SimpleHubStudioAutomation()
            
            if not automation.initialize(profile_id):
                self.log_message(f"❌ 工作线程 {worker_id} 环境启动失败 - 环境: {env_name} (可能是HubStudio连接问题或环境已被占用)", "ERROR")
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    self.upload_results['failed'] += 1
                return
            
            self.log_message(f"✅ 工作线程 {worker_id} 初始化成功 - 环境: {env_name}", "INFO")
            
            # 准备上传参数
            title = title_template if title_template else os.path.splitext(os.path.basename(video_path))[0]
            description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"
            
            self.log_message(f"工作线程 {worker_id} 开始上传: {os.path.basename(video_path)}", "INFO")
            
            # 执行上传
            success = automation.upload_single_video(video_path, title, description, children_content)
            
            # 更新统计
            with self.upload_results['lock']:
                self.upload_results['completed'] += 1
                if success:
                    self.upload_results['success'] += 1
                    self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {os.path.basename(video_path)}", "INFO")
                else:
                    self.upload_results['failed'] += 1
                    self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {os.path.basename(video_path)}", "ERROR")
                
                # 更新进度
                progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                self._trigger_callback('on_progress_update', progress)
                self._trigger_callback('on_status_update', 
                    f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                    f"成功: {self.upload_results['success']}")
            
        except Exception as e:
            with self.upload_results['lock']:
                self.upload_results['completed'] += 1
                self.upload_results['failed'] += 1
            self.log_message(f"工作线程 {worker_id} 上传异常: {os.path.basename(video_path)} - {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    
    def _multi_video_upload_worker(self, env, video_queue, title_template, description_template, children_content, worker_id):
        """多视频上传的工作线程（多视频模式）"""
        automation = None
        try:
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"🚀 工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            automation = SimpleHubStudioAutomation()
            if not automation.initialize(profile_id):
                self.log_message(f"❌ 工作线程 {worker_id} 环境启动失败 - 环境: {env_name} (可能是HubStudio连接问题或环境已被占用)", "ERROR")
                return
            
            self.log_message(f"✅ 工作线程 {worker_id} 初始化成功，开始处理视频队列", "INFO")
            
            # 处理视频队列
            while self.is_uploading:
                try:
                    # 从队列获取视频任务，超时1秒
                    video_index, video_path = video_queue.get(timeout=1)
                except queue.Empty:
                    # 队列为空，检查是否还有任务
                    if video_queue.empty():
                        break
                    continue
                
                try:
                    # 生成标题（多视频时添加序号）
                    if title_template:
                        title = f"{title_template} - {video_index + 1:03d}"
                    else:
                        title = os.path.splitext(os.path.basename(video_path))[0]
                    
                    description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"
                    
                    self.log_message(f"工作线程 {worker_id} 开始上传: {os.path.basename(video_path)}", "INFO")
                    
                    # 执行上传
                    success = automation.upload_single_video(video_path, title, description, children_content)
                    
                    # 更新统计
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        if success:
                            self.upload_results['success'] += 1
                            self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {os.path.basename(video_path)}", "INFO")
                        else:
                            self.upload_results['failed'] += 1
                            self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {os.path.basename(video_path)}", "ERROR")
                        
                        # 更新进度
                        progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                        self._trigger_callback('on_progress_update', progress)
                        self._trigger_callback('on_status_update', 
                            f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                            f"成功: {self.upload_results['success']}")
                    
                    # 标记任务完成
                    video_queue.task_done()
                    
                    # 上传间隔
                    if self.is_uploading:
                        interval = int(self.get_config('AUTOMATION', 'upload_interval', '5'))
                        time.sleep(interval)
                        
                except Exception as e:
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        self.upload_results['failed'] += 1
                    self.log_message(f"工作线程 {worker_id} 上传异常: {os.path.basename(video_path)} - {e}", "ERROR")
                    # 标记任务完成
                    video_queue.task_done()
            
            self.log_message(f"工作线程 {worker_id} 完成所有任务", "INFO")
            
        except Exception as e:
            self.log_message(f"工作线程 {worker_id} 异常: {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    
    # ==================== 状态管理 ====================
    
    def is_upload_in_progress(self):
        """检查是否正在上传"""
        return self.is_uploading
    
    def get_upload_results(self):
        """获取上传结果"""
        return self.upload_results.copy() if self.upload_results else {}