"""
HubStudio 视频上传工具 - 主程序入口
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from infrastructure import (
    Logger,
    ConfigManager,
    StateManager,
    ErrorHandler
)
from business import (
    EventManager,
    ConcurrencyManager,
    EnvironmentManager,
    UploadManager
)
from api import (
    HubStudioAPI,
    BrowserManager,
    FileManager
)
from gui import MainWindow


class HubStudioApp:
    """HubStudio 主应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.root = None
        self.main_window = None
        
        # 核心组件
        self.logger = None
        self.config_manager = None
        self.state_manager = None
        self.error_handler = None
        self.event_manager = None
        
        # 业务组件
        self.concurrency_manager = None
        self.environment_manager = None
        self.upload_manager = None
        
        # API组件
        self.hubstudio_api = None
        self.browser_manager = None
        self.file_manager = None
        
        # 应用状态
        self.is_running = False
        self.shutdown_event = threading.Event()
        
    def initialize(self):
        """初始化应用程序组件"""
        try:
            print("正在初始化 HubStudio...")
            
            # 1. 初始化基础设施层
            self._initialize_infrastructure()
            
            # 2. 初始化业务逻辑层
            self._initialize_business_layer()
            
            # 3. 初始化API层
            self._initialize_api_layer()
            
            # 4. 初始化GUI层
            self._initialize_gui_layer()
            
            # 5. 设置信号处理
            self._setup_signal_handlers()
            
            print("HubStudio 初始化完成！")
            return True
            
        except Exception as e:
            error_msg = f"应用程序初始化失败: {e}"
            print(error_msg)
            if self.logger:
                self.logger.error(error_msg)
            messagebox.showerror("初始化错误", error_msg)
            return False
    
    def _initialize_infrastructure(self):
        """初始化基础设施层"""
        print("  - 初始化基础设施层...")
        
        # 配置管理器
        self.config_manager = ConfigManager()
        
        # 日志系统
        self.logger = Logger(self.config_manager)
        self.logger.info("日志系统已启动")
        
        # 状态管理器
        self.state_manager = StateManager(self.logger)
        
        # 错误处理器
        self.error_handler = ErrorHandler(self.logger)
        
        # 事件管理器
        self.event_manager = EventManager(self.logger)
        
        self.logger.info("基础设施层初始化完成")
    
    def _initialize_business_layer(self):
        """初始化业务逻辑层"""
        print("  - 初始化业务逻辑层...")
        
        # 并发管理器
        self.concurrency_manager = ConcurrencyManager(
            self.config_manager,
            self.logger
        )
        
        # 环境管理器
        self.environment_manager = EnvironmentManager(
            self.config_manager,
            self.event_manager,
            self.logger
        )
        
        # 上传管理器
        self.upload_manager = UploadManager(
            self.config_manager,
            self.event_manager,
            self.concurrency_manager,
            self.logger
        )
        
        self.logger.info("业务逻辑层初始化完成")
    
    def _initialize_api_layer(self):
        """初始化API层"""
        print("  - 初始化API层...")
        
        # HubStudio API
        self.hubstudio_api = HubStudioAPI(
            self.config_manager,
            self.event_manager,
            self.logger
        )
        
        # 浏览器管理器
        self.browser_manager = BrowserManager(
            self.config_manager,
            self.hubstudio_api,
            self.logger
        )
        
        # 文件管理器
        self.file_manager = FileManager(
            self.config_manager,
            self.logger
        )
        
        self.logger.info("API层初始化完成")
    
    def _initialize_gui_layer(self):
        """初始化GUI层"""
        print("  - 初始化GUI层...")
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("HubStudio 视频上传工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果存在）
        try:
            icon_path = project_root / "assets" / "icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception as e:
            self.logger.warning(f"无法加载窗口图标: {e}")
        
        # 创建主窗口组件
        self.main_window = MainWindow(
            self.root,
            self.event_manager,
            self.config_manager,
            self.logger,
            self.file_manager,
            self.upload_manager
        )
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        
        self.logger.info("GUI层初始化完成")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            # 设置SIGINT和SIGTERM处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            self.logger.info("信号处理器设置完成")
        except Exception as e:
            self.logger.warning(f"设置信号处理器失败: {e}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，开始关闭应用程序...")
        self.shutdown()
    
    def _on_window_close(self):
        """窗口关闭事件处理"""
        try:
            if messagebox.askokcancel("退出", "确定要退出 HubStudio 吗？"):
                self.shutdown()
        except Exception as e:
            self.logger.error(f"处理窗口关闭事件失败: {e}")
            self.shutdown()
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                return False
            
            self.is_running = True
            self.logger.info("HubStudio 应用程序启动")
            
            # 启动GUI主循环
            self.root.mainloop()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("用户中断，正在关闭应用程序...")
            self.shutdown()
            return True
            
        except Exception as e:
            error_msg = f"应用程序运行时发生错误: {e}"
            self.logger.error(error_msg)
            messagebox.showerror("运行时错误", error_msg)
            return False
        
        finally:
            self.cleanup()
    
    def shutdown(self):
        """关闭应用程序"""
        try:
            if not self.is_running:
                return
            
            self.logger.info("正在关闭 HubStudio...")
            self.is_running = False
            self.shutdown_event.set()
            
            # 停止各个组件
            if self.upload_manager:
                self.upload_manager.stop_all_uploads()
            
            if self.concurrency_manager:
                self.concurrency_manager.shutdown()
            
            if self.browser_manager:
                self.browser_manager.close_all_browsers()
            
            # 保存配置和状态
            if self.config_manager:
                self.config_manager.save()
            
            if self.state_manager:
                self.state_manager.save_state()
            
            # 关闭GUI
            if self.root:
                self.root.quit()
                self.root.destroy()
            
            self.logger.info("HubStudio 已关闭")
            
        except Exception as e:
            print(f"关闭应用程序时发生错误: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理临时文件
            temp_dir = project_root / "temp"
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
            
            # 关闭日志系统
            if self.logger:
                self.logger.close()
            
        except Exception as e:
            print(f"清理资源时发生错误: {e}")


def check_dependencies():
    """检查依赖项"""
    try:
        import requests
        import selenium
        import tkinter
        return True
    except ImportError as e:
        print(f"缺少必要的依赖项: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def create_directories():
    """创建必要的目录"""
    try:
        directories = [
            "logs",
            "temp", 
            "config",
            "data"
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        return True
    except Exception as e:
        print(f"创建目录失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("=" * 50)
        print("HubStudio 视频上传工具")
        print("版本: 1.0.0")
        print("=" * 50)
        
        # 检查依赖项
        if not check_dependencies():
            input("按回车键退出...")
            return 1
        
        # 创建必要目录
        if not create_directories():
            input("按回车键退出...")
            return 1
        
        # 创建并运行应用程序
        app = HubStudioApp()
        
        if app.run():
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
        return 1


if __name__ == "__main__":
    sys.exit(main())